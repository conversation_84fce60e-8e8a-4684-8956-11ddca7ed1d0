<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <!--  Title -->
    <title>Daftar sebagai <?= ucfirst($type) ?> - <PERSON><PERSON><PERSON> Go</title>
    <!--  Required Meta Tag -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="handheldfriendly" content="true" />
    <meta name="MobileOptimized" content="width" />
    <meta name="description" content="Daftar Ngaji Go" />
    <meta name="author" content="" />
    <meta name="keywords" content="Daftar" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--  Favicon -->
    <link rel="shortcut icon" type="image/png" href="<?= base_url('assets/images/logos/favicon.png'); ?>" />
    <!-- Core Css -->
    <link id="themeColors" rel="stylesheet" href="<?= base_url('assets/css/style.min.css'); ?>" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
</head>

<body>
    <!-- Preloader -->
    <div class="preloader">
        <img src="<?= base_url('assets/images/logos/favicon.png'); ?>" alt="loader" class="lds-ripple img-fluid" />
    </div>
    <!--  Body Wrapper -->
    <div class="page-wrapper" id="main-wrapper" data-layout="vertical" data-sidebartype="full"
        data-sidebar-position="fixed" data-header-position="fixed">
        <div
            class="position-relative overflow-hidden radial-gradient min-vh-100 d-flex align-items-center justify-content-center p-5">
            <div class="d-flex align-items-center justify-content-center w-100">
                <div class="row justify-content-center w-100">
                    <div class="col-md-12 col-lg-8 col-xxl-6">
                        <div class="card mb-0">
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-sm-6">
                                        <h2 class="mb-3 fs-7 fw-bolder"><?= $title; ?></h2>
                                    </div>

                                    <div class="col-sm-6 text-end">
                                        <div class="btn-group w-100" role="group">
                                            <a href="<?= site_url('auth/register/member'); ?>"
                                                class="btn <?= ($type == 'member') ? 'btn-primary' : 'btn-outline-primary'; ?>">Member</a>
                                            <a href="<?= site_url('auth/register/guru'); ?>"
                                                class="btn <?= ($type == 'guru') ? 'btn-primary' : 'btn-outline-primary'; ?>">Guru</a>
                                            <a href="<?= site_url('auth/register/tpa'); ?>"
                                                class="btn <?= ($type == 'tpa') ? 'btn-primary' : 'btn-outline-primary'; ?>">TPA</a>
                                        </div>
                                    </div>
                                </div>
                                <p class="mb-4">Silahkan lengkapi data berikut untuk mendaftar sebagai
                                    <?= $type; ?>. <small class="text-danger">* Wajib diisi</small></p>

                                <?php if (isset($error)): ?>
                                <?= $error; ?>
                                <?php endif; ?>

                                <?php if ($this->session->flashdata('message')): ?>
                                <?= $this->session->flashdata('message'); ?>
                                <?php endif; ?>

                                <!-- Di bagian awal form, tambahkan alert untuk menampilkan error validasi secara keseluruhan -->
                                <style>
                                .alert p {
                                    margin-bottom: 0;
                                }
                                </style>
                                <?php if (validation_errors()): ?>
                                <div class="alert alert-danger">
                                    <h5><i class="fas fa-exclamation-triangle"></i> Terdapat kesalahan pada form:</h5>
                                    <ul class="mb-0">
                                        <?php
                                            // Ambil semua error validasi
                                            $errors = explode("\n", validation_errors());
                                            foreach ($errors as $error) {
                                                $error = trim($error);
                                                if (!empty($error)) {
                                                    echo "<li>" . $error . "</li>";
                                                }
                                            }
                                            ?>
                                    </ul>
                                </div>
                                <?php endif; ?>

                                <?= form_open_multipart('auth/register/' . $type, ['class' => 'validation-wizard wizard-circle']); ?>

                                <!-- Tambahkan input hidden untuk menyimpan tab aktif -->
                                <input type="hidden" id="activeTab" name="activeTab"
                                    value="<?= $this->session->flashdata('active_tab') ?: '0'; ?>">

                                <!-- Step 1: Data Utama -->
                                <h6>Data Utama</h6>
                                <section>
                                    <div class="card card-body">
                                        <!-- Form fields for all user types -->
                                        <div class="mb-4">
                                            <label for="fullName" class="form-label">Nama Lengkap <span
                                                    class="text-danger">*</span></label>
                                            <input type="text" class="form-control required" id="fullName"
                                                name="fullName" value="<?= set_value('fullName'); ?>">
                                            <?= form_error('fullName', '<small class="text-danger">', '</small>'); ?>


                                            <div class="mb-3">
                                                <label for="whatsapp" class="form-label">Nomor WhatsApp Aktif <span
                                                        class="text-danger">*</span></label>
                                                <input type="text" class="form-control required" id="whatsapp"
                                                    name="whatsapp" value="<?= set_value('whatsapp'); ?>">
                                                <?= form_error('whatsapp', '<small class="text-danger">', '</small>'); ?>
                                            </div>

                                            <div class="mb-3">
                                                <label for="email" class="form-label">Email <span
                                                        class="text-danger">*</span></label>
                                                <input type="email" class="form-control required" id="email"
                                                    name="email" value="<?= set_value('email'); ?>">
                                                <?= form_error('email', '<small class="text-danger">', '</small>'); ?>
                                            </div>

                                            <!-- Jenis Kelamin -->
                                            <div class="mb-3">
                                                <label class="form-label">Jenis Kelamin <span
                                                        class="text-danger">*</span></label>
                                                <div class="d-flex gap-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="gender"
                                                            id="genderMale" value="L"
                                                            <?= set_radio('gender', 'laki-laki'); ?> required>
                                                        <label class="form-check-label" for="genderMale">
                                                            <i class="ti ti-user me-1"></i>Laki-laki
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="gender"
                                                            id="genderFemale" value="perempuan"
                                                            <?= set_radio('gender', 'perempuan'); ?> required>
                                                        <label class="form-check-label" for="genderFemale">
                                                            <i class="ti ti-user me-1"></i>Perempuan
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="gender"
                                                            id="genderOther" value="lainnya"
                                                            <?= set_radio('gender', 'lainnya'); ?> required>
                                                        <label class="form-check-label" for="genderOther">
                                                            <i class="ti ti-user me-1"></i>Lainnya
                                                        </label>
                                                    </div>
                                                </div>
                                                <?= form_error('gender', '<small class="text-danger">', '</small>'); ?>
                                            </div>

                                            <!-- Foto Profil -->
                                            <div class="mb-3">
                                                <label for="profilePhoto" class="form-label">Foto Profil <span
                                                        class="text-danger">*</span></label>
                                                <input type="file" class="form-control required" id="profilePhoto"
                                                    name="profilePhoto" accept="image/*">
                                                <small class="text-muted">Format: JPG, PNG, GIF. Maks: 2MB</small>
                                                <?= isset($error_profilePhoto) ? '<small class="text-danger">' . $error_profilePhoto . '</small>' : ''; ?>
                                            </div>

                                            <!-- Upload KTP -->
                                            <div class="mb-3">
                                                <label for="identityCard" class="form-label">Identitas KTP <span
                                                        class="text-danger">*</span></label>
                                                <input type="file" class="form-control required" id="identityCard"
                                                    name="identityCard" accept="image/*">
                                                <small class="text-muted">Format: JPG, PNG. Maks: 2MB</small>
                                                <?= isset($error_identityCard) ? '<small class="text-danger">' . $error_identityCard . '</small>' : ''; ?>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Alamat <span
                                                        class="text-danger">*</span></label>
                                                <div class="card p-3 border">
                                                    <div class="mb-3">
                                                        <label for="province" class="form-label">Provinsi <span
                                                                class="text-danger">*</span></label>
                                                        <select class="form-select" id="province" name="province"
                                                            autocomplete="off">
                                                            <option value="" selected>Pilih Provinsi</option>
                                                            <?php foreach ($provinces as $province): ?>
                                                            <option value="<?= $province->id; ?>"
                                                                <?= set_select('province', $province->id); ?>>
                                                                <?= $province->name; ?></option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                        <?= form_error('province', '<small class="text-danger">', '</small>'); ?>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="city" class="form-label">Kabupaten/Kota <span
                                                                class="text-danger">*</span></label>
                                                        <select class="form-select" id="city" name="city"
                                                            autocomplete="off">
                                                            <option value="" selected>Pilih Kabupaten/Kota</option>
                                                            <!-- Opsi kabupaten/kota akan diisi dengan JavaScript -->
                                                        </select>
                                                        <?= form_error('city', '<small class="text-danger">', '</small>'); ?>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="district" class="form-label">Kecamatan <span
                                                                class="text-danger">*</span></label>
                                                        <select class="form-select" id="district" name="district"
                                                            autocomplete="off">
                                                            <option value="" selected>Pilih Kecamatan</option>
                                                            <!-- Opsi kecamatan akan diisi dengan JavaScript -->
                                                        </select>
                                                        <?= form_error('district', '<small class="text-danger">', '</small>'); ?>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="village" class="form-label">Kelurahan/Desa <span
                                                                class="text-danger">*</span></label>
                                                        <select class="form-select" id="village" name="village"
                                                            autocomplete="off">
                                                            <option value="" selected>Pilih Kelurahan/Desa</option>
                                                            <!-- Opsi kelurahan/desa akan diisi dengan JavaScript -->
                                                        </select>
                                                        <?= form_error('village', '<small class="text-danger">', '</small>'); ?>
                                                    </div>

                                                    <div class="mb-0">
                                                        <label for="address" class="form-label">Alamat Lengkap <span
                                                                class="text-danger">*</span></label>
                                                        <textarea class="form-control required" id="address"
                                                            name="address" rows="3"
                                                            placeholder="Masukkan alamat lengkap"
                                                            autocomplete="off"><?= set_value('address'); ?></textarea>
                                                        <?= form_error('address', '<small class="text-danger">', '</small>'); ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- TPA Tempat Bekerja (Opsional) -->
                                            <div class="mb-3">
                                                <label for="tpaWorkplace" class="form-label">TPA Tempat Bekerja
                                                    (Opsional)</label>
                                                <select class="form-select" id="tpaWorkplace" name="tpaWorkplace">
                                                    <option value="" selected>Pilih TPA</option>
                                                    <!-- Opsi TPA akan diisi dengan JavaScript -->
                                                </select>
                                                <?= form_error('tpaWorkplace', '<small class="text-danger">', '</small>'); ?>
                                            </div>
                                        </div>
                                </section>

                                <!-- Step 2: Data Diri -->
                                <h6>Data Diri</h6>
                                <section>
                                    <div class="card card-body">
                                        <div class="mb-3">
                                            <label for="selfDescription" class="form-label">Deskripsi Diri <span
                                                    class="text-danger">*</span></label>
                                            <textarea class="form-control required" id="selfDescription"
                                                name="selfDescription" rows="5"
                                                placeholder="Ceritakan tentang diri Anda, pengalaman mengajar, dan keahlian yang dimiliki"><?= set_value('selfDescription'); ?></textarea>
                                            <?= form_error('selfDescription', '<small class="text-danger">', '</small>'); ?>
                                        </div>

                                        <div class="mb-3">
                                            <label for="teachingExperience" class="form-label">Pengalaman Mengajar
                                                (Tahun) <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control required" id="teachingExperience"
                                                name="teachingExperience" min="0"
                                                value="<?= set_value('teachingExperience', '0'); ?>">
                                            <?= form_error('teachingExperience', '<small class="text-danger">', '</small>'); ?>
                                        </div>

                                        <!-- Keahlian & Materi yang Diajarkan -->
                                        <div class="mb-4">
                                            <label class="form-label">Keahlian & Materi yang Diajarkan <span
                                                    class="text-danger">*</span></label>
                                            <div class="card p-3 border">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered" id="skillsTable">
                                                        <thead>
                                                            <tr>
                                                                <th>Keahlian</th>
                                                                <th>Tingkat Kemampuan</th>
                                                                <th>Aksi</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td colspan="3" class="text-center">Belum ada data
                                                                    keahlian</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>

                                                <input type="hidden" id="skillsData" name="skillsData"
                                                    value="<?= set_value('skillsData'); ?>">
                                                <?= form_error('skillsData', '<small class="text-danger">', '</small>'); ?>
                                                <div id="skillsValidationMessage" class="mt-2"></div>

                                                <div class="d-flex justify-content-end mt-2">
                                                    <button type="button" class="btn btn-primary" id="addSkill">
                                                        <i class="fas fa-plus me-1"></i> Tambah Keahlian
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Riwayat Pendidikan -->
                                        <div class="mb-4">
                                            <label class="form-label">Riwayat Pendidikan <span
                                                    class="text-danger">*</span></label>
                                            <div class="card p-3 border">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered" id="educationTable">
                                                        <thead>
                                                            <tr>
                                                                <th>Jenjang</th>
                                                                <th>Institusi</th>
                                                                <th>Tahun Lulus</th>
                                                                <th>Aksi</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td colspan="4" class="text-center">Belum ada data
                                                                    pendidikan</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>

                                                <input type="hidden" id="educationData" name="educationData"
                                                    value="<?= set_value('educationData'); ?>" required>
                                                <?= form_error('educationData', '<small class="text-danger">', '</small>'); ?>
                                                <div id="educationValidationMessage" class="mt-2"></div>

                                                <div class="d-flex justify-content-end mt-2">
                                                    <button type="button" class="btn btn-primary" id="addEducation">
                                                        <i class="fas fa-plus me-1"></i> Tambah Pendidikan
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </section>

                                <!-- Step 3: Jadwal & Tarif -->
                                <h6>Jadwal & Tarif</h6>
                                <section>
                                    <div class="card card-body">
                                        <!-- Jadwal Tersedia -->
                                        <div class="mb-4">
                                            <label class="form-label">Jadwal Tersedia <span
                                                    class="text-danger">*</span></label>
                                            <div class="card p-3 border">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered" id="scheduleTable">
                                                        <thead>
                                                            <tr>
                                                                <th>Hari</th>
                                                                <th>Jam Mulai</th>
                                                                <th>Jam Selesai</th>
                                                                <th>Aksi</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td colspan="4" class="text-center">Belum ada data
                                                                    jadwal</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>

                                                <input type="hidden" id="scheduleData" name="scheduleData"
                                                    value="<?= set_value('scheduleData'); ?>">
                                                <?= form_error('scheduleData', '<small class="text-danger">', '</small>'); ?>
                                                <div id="scheduleValidationMessage" class="mt-2"></div>

                                                <div class="d-flex justify-content-end mt-2">
                                                    <button type="button" class="btn btn-primary" id="addSchedule">
                                                        <i class="fas fa-plus me-1"></i> Tambah Jadwal
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Tarif Mengajar -->
                                        <div class="mb-4">
                                            <label class="form-label">Tarif Mengajar <span
                                                    class="text-danger">*</span></label>
                                            <div class="card p-3 border">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered" id="rateTable">
                                                        <thead>
                                                            <tr>
                                                                <th>Jenis Layanan</th>
                                                                <th>Durasi (Menit)</th>
                                                                <th>Tarif (Rp)</th>
                                                                <th>Aksi</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td colspan="4" class="text-center">Belum ada data tarif
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>

                                                <input type="hidden" id="rateData" name="rateData"
                                                    value="<?= set_value('rateData'); ?>">
                                                <?= form_error('rateData', '<small class="text-danger">', '</small>'); ?>
                                                <div id="rateValidationMessage" class="mt-2"></div>

                                                <div class="d-flex justify-content-end mt-2">
                                                    <button type="button" class="btn btn-primary" id="addRate">
                                                        <i class="fas fa-plus me-1"></i> Tambah Tarif
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="password" class="form-label">Password <span
                                                    class="text-danger">*</span></label>
                                            <input type="password" class="form-control" id="password" name="password"
                                                required>
                                            <?= form_error('password', '<small class="text-danger">', '</small>'); ?>
                                        </div>

                                        <div class="mb-4">
                                            <label for="confirmPassword" class="form-label">Konfirmasi Password <span
                                                    class="text-danger">*</span></label>
                                            <input type="password" class="form-control" id="confirmPassword"
                                                name="confirmPassword" required>
                                            <?= form_error('confirmPassword', '<small class="text-danger">', '</small>'); ?>
                                        </div>
                                    </div>
                                </section>

                                <div class="d-flex align-items-center mt-4">
                                    <p class="fs-4 mb-0 text-dark">Sudah memiliki Akun?</p>
                                    <a class="text-primary fw-medium ms-2"
                                        href="<?= site_url('auth/login'); ?>">Masuk</a>
                                </div>
                                <?= form_close(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--  Import Js Files -->
    <script src="<?= base_url('assets/libs/jquery/dist/jquery.min.js'); ?>"></script>
    <script src="<?= base_url('assets/libs/simplebar/dist/simplebar.min.js'); ?>"></script>
    <script src="<?= base_url('assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js'); ?>"></script>
    <!-- core files -->
    <script src="<?= base_url('assets/js/app.min.js') ?>"></script>
    <script src="<?= base_url('assets/js/app.init.js') ?>"></script>
    <script src="<?= base_url('assets/js/app-style-switcher.js') ?>"></script>
    <script src="<?= base_url('assets/js/sidebarmenu.js') ?>"></script>

    <script src="<?= base_url('assets/js/custom.js') ?>"></script>

    <!-- Form wizard -->
    <script src="<?= base_url('assets/libs/jquery-steps/build/jquery.steps.min.js'); ?>"></script>
    <script src="<?= base_url('assets/libs/jquery-validation/dist/jquery.validate.min.js'); ?>"></script>

    <script>
    // Tambahkan custom validation methods sebelum form wizard diinisialisasi
    $.validator.addMethod("validProvince", function(value, element) {
        if (!value) return true; // Biarkan rule 'required' yang menangani ini

        // Cek apakah provinsi ada dalam dropdown
        return $("#province option[value='" + value + "']").length > 0;
    }, "Provinsi yang dipilih tidak valid");

    $.validator.addMethod("validCity", function(value, element) {
        if (!value) return true; // Biarkan rule 'required' yang menangani ini

        const provinceId = $("#province").val();
        if (!provinceId) return false;

        // Cek apakah kota ada dalam dropdown dan sesuai dengan provinsi
        return $("#city option[value='" + value + "']").length > 0;
    }, "Kabupaten/Kota yang dipilih tidak valid atau tidak sesuai dengan provinsi");

    $.validator.addMethod("validDistrict", function(value, element) {
        if (!value) return true; // Biarkan rule 'required' yang menangani ini

        const cityId = $("#city").val();
        if (!cityId) return false;

        // Cek apakah kecamatan ada dalam dropdown dan sesuai dengan kota
        return $("#district option[value='" + value + "']").length > 0;
    }, "Kecamatan yang dipilih tidak valid atau tidak sesuai dengan kabupaten/kota");

    $.validator.addMethod("validVillage", function(value, element) {
        if (!value) return true; // Biarkan rule 'required' yang menangani ini

        const districtId = $("#district").val();
        if (!districtId) return false;

        // Cek apakah kelurahan/desa ada dalam dropdown dan sesuai dengan kecamatan
        return $("#village option[value='" + value + "']").length > 0;
    }, "Kelurahan/Desa yang dipilih tidak valid atau tidak sesuai dengan kecamatan");

    // Tambahkan custom validation methods untuk keahlian dan pendidikan
    $.validator.addMethod("validSkillsData", function(value, element) {
        if (!value || value === '[]') {
            return false;
        }

        try {
            const skillsArray = JSON.parse(value);
            return Array.isArray(skillsArray) && skillsArray.length > 0;
        } catch (e) {
            return false;
        }
    }, "Minimal satu keahlian harus ditambahkan");

    $.validator.addMethod("validEducationData", function(value, element) {
        if (!value || value === '[]') {
            return false;
        }

        try {
            const educationArray = JSON.parse(value);
            return Array.isArray(educationArray) && educationArray.length > 0;
        } catch (e) {
            return false;
        }
    }, "Minimal satu riwayat pendidikan harus ditambahkan");

    // Tambahkan custom validation methods untuk jadwal dan tarif
    $.validator.addMethod("validScheduleData", function(value, element) {
        if (!value || value === '[]') {
            return false;
        }

        try {
            const scheduleArray = JSON.parse(value);
            return Array.isArray(scheduleArray) && scheduleArray.length > 0;
        } catch (e) {
            return false;
        }
    }, "Minimal satu jadwal harus ditambahkan");

    $.validator.addMethod("validRateData", function(value, element) {
        if (!value || value === '[]') {
            return false;
        }

        try {
            const rateArray = JSON.parse(value);
            return Array.isArray(rateArray) && rateArray.length > 0;
        } catch (e) {
            return false;
        }
    }, "Minimal satu tarif harus ditambahkan");

    // Simpan nilai yang sudah dipilih sebelumnya (dari set_value)
    const selectedProvinceId = '<?= set_value('province'); ?>';
    const selectedCityId = '<?= set_value('city'); ?>';
    const selectedDistrictId = '<?= set_value('district'); ?>';
    const selectedVillageId = '<?= set_value('village'); ?>';

    // Fungsi untuk setup event listeners
    function setupEventListeners() {
        // Event listener untuk perubahan provinsi
        $('#province').off('change').on('change', handleProvinceChange);

        if (selectedProvinceId) {
            loadCities(selectedProvinceId);
        }

        // Event listener untuk perubahan kabupaten/kota
        $("#city").off('change').on('change', handleCityChange);

        // Event listener untuk perubahan kecamatan
        $("#district").off('change').on('change', handleDistrictChange);
    }

    // Handler untuk perubahan provinsi
    function handleProvinceChange() {
        const provinceId = $(this).val();

        // Reset dropdown kota, kecamatan, dan desa saat provinsi diubah
        $("#city").html('<option value="" selected>Pilih Kabupaten/Kota</option>');
        $("#district").html('<option value="" selected>Pilih Kecamatan</option>');
        $("#village").html('<option value="" selected>Pilih Kelurahan/Desa</option>');

        // Reset validasi error untuk field yang terkait
        $("#city").removeClass('error').next('label.error').remove();
        $("#district").removeClass('error').next('label.error').remove();
        $("#village").removeClass('error').next('label.error').remove();

        if (provinceId) {
            loadCities(provinceId);
        }
    }

    // Handler untuk perubahan kabupaten/kota
    function handleCityChange() {
        const cityId = $(this).val();

        // Reset dropdown kecamatan dan desa saat kota diubah
        $("#district").html('<option value="" selected>Pilih Kecamatan</option>');
        $("#village").html('<option value="" selected>Pilih Kelurahan/Desa</option>');

        // Reset validasi error untuk field yang terkait
        $("#district").removeClass('error').next('label.error').remove();
        $("#village").removeClass('error').next('label.error').remove();

        if (cityId) {
            loadDistricts(cityId);
        }
    }

    // Handler untuk perubahan kecamatan
    function handleDistrictChange() {
        const districtId = $(this).val();

        // Reset dropdown desa saat kecamatan diubah
        $("#village").html('<option value="" selected>Pilih Kelurahan/Desa</option>');

        // Reset validasi error untuk field yang terkait
        $("#village").removeClass('error').next('label.error').remove();

        if (districtId) {
            loadVillages(districtId);
        }
    }

    // Fungsi untuk memuat data kabupaten/kota
    function loadCities(provinceId) {
        // Tampilkan loading state
        const $citySelect = $("#city");
        if ($citySelect.length === 0) {
            return;
        }

        $citySelect.html('<option value="">Loading...</option>');

        // Kirim request POST ke API
        $.ajax({
            url: '<?= site_url('api/location/cities'); ?>',
            type: 'POST',
            data: {
                provinceid: provinceId
            },
            dataType: 'json',
            success: function(data) {
                // Reset dropdown
                $citySelect.html('<option value="" selected>Pilih Kabupaten/Kota</option>');

                // Periksa apakah response berhasil
                if (data.status === 'success' && Array.isArray(data.data)) {
                    // Tambahkan opsi kota dari response
                    $.each(data.data, function(index, city) {
                        const $option = $('<option></option>')
                            .val(city.id)
                            .text(city.name);

                        // Pilih kota yang sesuai dengan data sebelumnya
                        if (city.id == selectedCityId) {
                            $option.prop('selected', true);
                        }

                        $citySelect.append($option);
                    });

                    // Jika ada kota yang dipilih sebelumnya, muat data kecamatan
                    if (selectedCityId) {
                        loadDistricts(selectedCityId);
                    }
                }
            },
            error: function() {
                $citySelect.html('<option value="" selected>Error loading data</option>');
            }
        });
    }

    // Fungsi untuk memuat data kecamatan
    function loadDistricts(cityId) {
        // Tampilkan loading state
        const $districtSelect = $("#district");
        if ($districtSelect.length === 0) {
            return;
        }

        $districtSelect.html('<option value="">Loading...</option>');

        // Kirim request POST ke API
        $.ajax({
            url: '<?= site_url('api/location/districts'); ?>',
            type: 'POST',
            data: {
                cityid: cityId
            },
            dataType: 'json',
            success: function(data) {
                // Reset dropdown
                $districtSelect.html('<option value="" selected>Pilih Kecamatan</option>');

                // Periksa apakah response berhasil
                if (data.status === 'success' && Array.isArray(data.data)) {
                    // Tambahkan opsi kecamatan dari response
                    $.each(data.data, function(index, district) {
                        const $option = $('<option></option>')
                            .val(district.id)
                            .text(district.name);

                        // Pilih kecamatan yang sesuai dengan data sebelumnya
                        if (district.id == selectedDistrictId) {
                            $option.prop('selected', true);
                        }

                        $districtSelect.append($option);
                    });

                    // Jika ada kecamatan yang dipilih sebelumnya, muat data desa
                    if (selectedDistrictId) {
                        loadVillages(selectedDistrictId);
                    }
                }
            },
            error: function() {
                $districtSelect.html('<option value="" selected>Error loading data</option>');
            }
        });
    }

    // Fungsi untuk memuat data kelurahan/desa
    function loadVillages(districtId) {
        // Tampilkan loading state
        const $villageSelect = $("#village");
        if ($villageSelect.length === 0) {
            return;
        }

        $villageSelect.html('<option value="">Loading...</option>');

        // Kirim request POST ke API
        $.ajax({
            url: '<?= site_url('api/location/villages'); ?>',
            type: 'POST',
            data: {
                districtid: districtId
            },
            dataType: 'json',
            success: function(data) {
                // Reset dropdown
                $villageSelect.html('<option value="" selected>Pilih Kelurahan/Desa</option>');

                // Periksa apakah response berhasil
                if (data.status === 'success' && Array.isArray(data.data)) {
                    // Tambahkan opsi desa dari response
                    $.each(data.data, function(index, village) {
                        const $option = $('<option></option>')
                            .val(village.id)
                            .text(village.name);

                        // Pilih desa yang sesuai dengan data sebelumnya
                        if (village.id == selectedVillageId) {
                            $option.prop('selected', true);
                        }

                        $villageSelect.append($option);
                    });
                }
            },
            error: function() {
                $villageSelect.html('<option value="" selected>Error loading data</option>');
            }
        });
    }

    // ===== CRUD KEAHLIAN =====
    let skills = [];
    try {
        // Coba ambil data dari flashdata jika ada
        const flashSkillsData = '<?= $this->session->flashdata("skills_data"); ?>';
        if (flashSkillsData) {
            skills = JSON.parse(flashSkillsData);
        } else {
            // Jika tidak ada flashdata, coba ambil dari input hidden
            skills = JSON.parse($("#skillsData").val() || '[]');
        }
    } catch (e) {
        skills = [];
    }

    // Update input hidden dengan data yang sudah ada
    $("#skillsData").val(JSON.stringify(skills));

    // ===== CRUD PENDIDIKAN =====
    let educations = [];
    try {
        // Coba ambil data dari flashdata jika ada
        const flashEducationData = '<?= $this->session->flashdata("education_data"); ?>';
        if (flashEducationData) {
            educations = JSON.parse(flashEducationData);
        } else {
            // Jika tidak ada flashdata, coba ambil dari input hidden
            educations = JSON.parse($("#educationData").val() || '[]');
        }
    } catch (e) {
        educations = [];
    }

    // Update input hidden dengan data yang sudah ada
    $("#educationData").val(JSON.stringify(educations));

    // ===== CRUD JADWAL =====
    let schedules = [];
    try {
        // Coba ambil data dari flashdata jika ada
        const flashScheduleData = '<?= $this->session->flashdata("schedule_data"); ?>';
        if (flashScheduleData) {
            schedules = JSON.parse(flashScheduleData);
        } else {
            // Jika tidak ada flashdata, coba ambil dari input hidden
            schedules = JSON.parse($("#scheduleData").val() || '[]');
        }
    } catch (e) {
        schedules = [];
    }

    // Update input hidden dengan data yang sudah ada
    $("#scheduleData").val(JSON.stringify(schedules));

    // ===== CRUD TARIF =====
    let rates = [];
    try {
        // Coba ambil data dari flashdata jika ada
        const flashRateData = '<?= $this->session->flashdata("rate_data"); ?>';
        if (flashRateData) {
            rates = JSON.parse(flashRateData);
        } else {
            // Jika tidak ada flashdata, coba ambil dari input hidden
            rates = JSON.parse($("#rateData").val() || '[]');
        }
    } catch (e) {
        rates = [];
    }

    // Update input hidden dengan data yang sudah ada
    $("#rateData").val(JSON.stringify(rates));

    // Fungsi untuk menampilkan data keahlian dalam tabel
    function renderSkillsTable() {
        const $tbody = $("#skillsTable tbody");
        $tbody.empty();

        if (skills.length === 0) {
            $tbody.append('<tr><td colspan="3" class="text-center">Belum ada data keahlian</td></tr>');
        } else {
            $.each(skills, function(index, skill) {
                $tbody.append(`
                        <tr>
                            <td>${skill.name}</td>
                            <td>${skill.level}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-danger delete-skill" data-index="${index}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `);
            });

            // Tambahkan event listener untuk tombol hapus
            $(".delete-skill").on("click", function() {
                const index = $(this).data("index");
                skills.splice(index, 1);
                updateSkillsData();
                renderSkillsTable();
            });
        }
    }

    // Fungsi untuk memperbarui input hidden dengan data keahlian
    function updateSkillsData() {
        $("#skillsData").val(JSON.stringify(skills));
        // Trigger validasi ulang untuk field skillsData
        $("#skillsData").valid();
        // Update validasi message
        updateSkillsValidationStatus();
    }

    // Fungsi untuk update status validasi keahlian
    function updateSkillsValidationStatus() {
        const $message = $("#skillsValidationMessage");

        if (skills.length === 0) {
            $message.html();
        } else {
            $message.html('<small class="text-success"><i class="fas fa-check-circle me-1"></i>' + skills.length +
                ' keahlian telah ditambahkan</small>');
        }
    }

    // Tampilkan data keahlian yang sudah ada
    renderSkillsTable();

    // Fungsi untuk menampilkan data pendidikan dalam tabel
    function renderEducationTable() {
        const $tbody = $("#educationTable tbody");
        $tbody.empty();

        if (educations.length === 0) {
            $tbody.append('<tr><td colspan="4" class="text-center">Belum ada data pendidikan</td></tr>');
        } else {
            $.each(educations, function(index, edu) {
                $tbody.append(`
                        <tr>
                            <td>${edu.level}</td>
                            <td>${edu.institution}</td>
                            <td>${edu.graduationYear}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-danger delete-edu" data-index="${index}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `);
            });

            // Tambahkan event listener untuk tombol hapus
            $(".delete-edu").on("click", function() {
                const index = $(this).data("index");
                educations.splice(index, 1);
                updateEducationData();
                renderEducationTable();
            });
        }
    }

    // Fungsi untuk memperbarui input hidden dengan data pendidikan
    function updateEducationData() {
        $("#educationData").val(JSON.stringify(educations));
        // Trigger validasi ulang untuk field educationData
        $("#educationData").valid();
        // Update validasi message
        updateEducationValidationStatus();
    }

    // Fungsi untuk update status validasi pendidikan
    function updateEducationValidationStatus() {
        const $message = $("#educationValidationMessage");

        if (educations.length === 0) {
            $message.html();
        } else {
            $message.html('<small class="text-success"><i class="fas fa-check-circle me-1"></i>' + educations.length +
                ' riwayat pendidikan telah ditambahkan</small>');
        }
    }

    // Fungsi untuk memperbarui input hidden dengan data jadwal
    function updateScheduleData() {
        $("#scheduleData").val(JSON.stringify(schedules));
        // Trigger validasi ulang untuk field scheduleData
        $("#scheduleData").valid();
        // Update validasi message
        updateScheduleValidationStatus();
    }

    // Fungsi untuk update status validasi jadwal
    function updateScheduleValidationStatus() {
        const $message = $("#scheduleValidationMessage");

        if (schedules.length === 0) {
            $message.html();
        } else {
            $message.html('<small class="text-success"><i class="fas fa-check-circle me-1"></i>' + schedules.length +
                ' jadwal telah ditambahkan</small>');
        }
    }

    // Fungsi untuk memperbarui input hidden dengan data tarif
    function updateRateData() {
        $("#rateData").val(JSON.stringify(rates));
        // Trigger validasi ulang untuk field rateData
        $("#rateData").valid();
        // Update validasi message
        updateRateValidationStatus();
    }

    // Fungsi untuk update status validasi tarif
    function updateRateValidationStatus() {
        const $message = $("#rateValidationMessage");

        if (rates.length === 0) {
            $message.html();
        } else {
            $message.html('<small class="text-success"><i class="fas fa-check-circle me-1"></i>' + rates.length +
                ' tarif telah ditambahkan</small>');
        }
    }

    // Fungsi untuk menampilkan data jadwal dalam tabel
    function renderScheduleTable() {
        const $tbody = $("#scheduleTable tbody");
        $tbody.empty();

        if (schedules.length === 0) {
            $tbody.append('<tr><td colspan="4" class="text-center">Belum ada data jadwal</td></tr>');
        } else {
            $.each(schedules, function(index, schedule) {
                $tbody.append(`
                        <tr>
                            <td>${schedule.day}</td>
                            <td>${schedule.startTime}</td>
                            <td>${schedule.endTime}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-danger delete-schedule" data-index="${index}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `);
            });

            // Tambahkan event listener untuk tombol hapus
            $(".delete-schedule").on("click", function() {
                const index = $(this).data("index");
                schedules.splice(index, 1);
                updateScheduleData();
                renderScheduleTable();
            });
        }
    }

    // Fungsi untuk menampilkan data tarif dalam tabel
    function renderRateTable() {
        const $tbody = $("#rateTable tbody");
        $tbody.empty();

        if (rates.length === 0) {
            $tbody.append('<tr><td colspan="4" class="text-center">Belum ada data tarif</td></tr>');
        } else {
            $.each(rates, function(index, rate) {
                $tbody.append(`
                        <tr>
                            <td>${rate.service}</td>
                            <td>${rate.duration}</td>
                            <td>Rp ${parseInt(rate.price).toLocaleString('id-ID')}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-danger delete-rate" data-index="${index}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `);
            });

            // Tambahkan event listener untuk tombol hapus
            $(".delete-rate").on("click", function() {
                const index = $(this).data("index");
                rates.splice(index, 1);
                updateRateData();
                renderRateTable();
            });
        }
    }

    // Tampilkan data yang sudah ada
    renderSkillsTable();
    renderEducationTable();
    renderScheduleTable();
    renderRateTable();

    // Update status validasi awal
    updateSkillsValidationStatus();
    updateEducationValidationStatus();
    updateScheduleValidationStatus();
    updateRateValidationStatus();

    // Fungsi untuk menampilkan data jadwal dalam tabel
    function renderScheduleTable() {
        const $tbody = $("#scheduleTable tbody");
        $tbody.empty();

        if (schedules.length === 0) {
            $tbody.append('<tr><td colspan="4" class="text-center">Belum ada data jadwal</td></tr>');
        } else {
            $.each(schedules, function(index, schedule) {
                $tbody.append(`
                        <tr>
                            <td>${schedule.day}</td>
                            <td>${schedule.startTime}</td>
                            <td>${schedule.endTime}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-danger delete-schedule" data-index="${index}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `);
            });

            // Tambahkan event listener untuk tombol hapus
            $(".delete-schedule").on("click", function() {
                const index = $(this).data("index");
                schedules.splice(index, 1);
                updateScheduleData();
                renderScheduleTable();
            });
        }
    }

    // Fungsi untuk memperbarui input hidden dengan data jadwal
    function updateScheduleData() {
        $("#scheduleData").val(JSON.stringify(schedules));
    }

    // Tampilkan data jadwal yang sudah ada
    renderScheduleTable();

    // Fungsi untuk menampilkan data tarif dalam tabel
    function renderRateTable() {
        const $tbody = $("#rateTable tbody");
        $tbody.empty();

        if (rates.length === 0) {
            $tbody.append('<tr><td colspan="4" class="text-center">Belum ada data tarif</td></tr>');
        } else {
            $.each(rates, function(index, rate) {
                $tbody.append(`
                        <tr>
                            <td>${rate.service}</td>
                            <td>${rate.duration}</td>
                            <td>${new Intl.NumberFormat('id-ID').format(rate.price)}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-danger delete-rate" data-index="${index}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `);
            });

            // Tambahkan event listener untuk tombol hapus
            $(".delete-rate").on("click", function() {
                const index = $(this).data("index");
                rates.splice(index, 1);
                updateRateData();
                renderRateTable();
            });
        }
    }

    // Fungsi untuk memperbarui input hidden dengan data tarif
    function updateRateData() {
        $("#rateData").val(JSON.stringify(rates));
    }

    // Tampilkan data tarif yang sudah ada
    renderRateTable();

    // Variabel untuk menyimpan data form tarif sementara
    let tempRateData = {
        service: "",
        duration: "",
        price: ""
    };

    // Event listener untuk tombol tambah tarif
    $("#addRate").on("click", function() {
        // Buat modal untuk form tambah tarif
        const modalHtml = `
                <div class="modal fade" id="rateModal" tabindex="-1" aria-labelledby="rateModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="rateModalLabel">Tambah Tarif</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="service" class="form-label">Jenis Layanan</label>
                                    <input type="text" class="form-control" id="service" placeholder="Contoh: Privat, Kelompok" autocomplete="off" required>
                                    <div class="invalid-feedback" id="serviceFeedback"></div>
                                </div>
                                <div class="mb-3">
                                    <label for="duration" class="form-label">Durasi</label>
                                    <input type="text" class="form-control" id="duration" placeholder="Contoh: 60 menit, 2 jam" autocomplete="off" required>
                                    <div class="invalid-feedback" id="durationFeedback"></div>
                                </div>
                                <div class="mb-3">
                                    <label for="price" class="form-label">Tarif (Rp)</label>
                                    <input type="number" class="form-control" id="price" placeholder="Contoh: 100000" autocomplete="off" required>
                                    <div class="invalid-feedback" id="priceFeedback"></div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                <button type="button" class="btn btn-primary" id="saveRate">Simpan</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

        // Hapus modal lama jika ada
        $("#rateModal").remove();

        // Tambahkan modal baru ke body
        $("body").append(modalHtml);

        // Tampilkan modal
        const rateModal = new bootstrap.Modal($("#rateModal")[0]);
        rateModal.show();

        // Isi form dengan data sementara (auto-fill)
        $("#service").val(tempRateData.service);
        $("#duration").val(tempRateData.duration);
        $("#price").val(tempRateData.price);

        // Event listener untuk input fields (simpan nilai sementara)
        $("#service").on("input", function() {
            tempRateData.service = $(this).val();
        });

        $("#duration").on("input", function() {
            tempRateData.duration = $(this).val();
        });

        $("#price").on("input", function() {
            tempRateData.price = $(this).val();
        });

        // Event listener untuk tombol simpan
        $("#saveRate").on("click", function() {
            // Reset validasi
            $("#service").removeClass("is-invalid");
            $("#duration").removeClass("is-invalid");
            $("#price").removeClass("is-invalid");
            $("#serviceFeedback").text("");
            $("#durationFeedback").text("");
            $("#priceFeedback").text("");

            let isValid = true;
            const service = $("#service").val();
            const duration = $("#duration").val();
            const price = $("#price").val();

            // Validasi jenis layanan
            if (!service || service.trim() === "") {
                $("#service").addClass("is-invalid");
                $("#serviceFeedback").text("Jenis layanan harus diisi");
                isValid = false;
            }

            // Validasi durasi
            if (!duration || duration.trim() === "") {
                $("#duration").addClass("is-invalid");
                $("#durationFeedback").text("Durasi harus diisi");
                isValid = false;
            }

            // Validasi tarif
            if (!price) {
                $("#price").addClass("is-invalid");
                $("#priceFeedback").text("Tarif harus diisi");
                isValid = false;
            } else if (price <= 0) {
                $("#price").addClass("is-invalid");
                $("#priceFeedback").text("Tarif harus lebih dari 0");
                isValid = false;
            }

            // Jika validasi gagal, hentikan proses
            if (!isValid) {
                return;
            }

            // Tambahkan data tarif baru
            rates.push({
                service,
                duration,
                price
            });

            // Reset data sementara
            tempRateData = {
                service: "",
                duration: "",
                price: ""
            };

            // Perbarui data dan tampilan
            updateRateData();
            renderRateTable();

            // Tutup modal
            rateModal.hide();
        });
    });

    // Inisialisasi Form Wizard
    $(".validation-wizard").steps({
        headerTag: "h6",
        bodyTag: "section",
        transitionEffect: "fade",
        titleTemplate: '<span class="step">#index#</span> #title#',
        labels: {
            finish: "Daftar",
            next: "Selanjutnya",
            previous: "Sebelumnya"
        },
        onStepChanging: function(event, currentIndex, newIndex) {
            // Validasi form saat pindah step
            var form = $(this);

            // Selalu izinkan untuk kembali ke step sebelumnya
            if (currentIndex > newIndex) {
                return true;
            }

            // Validasi khusus untuk step Data Diri (index 1)
            if (currentIndex === 1 && newIndex > currentIndex) {
                // Cek apakah keahlian sudah ditambahkan
                const skillsData = $("#skillsData").val();
                let skillsArray = [];
                try {
                    skillsArray = JSON.parse(skillsData || '[]');
                } catch (e) {
                    skillsArray = [];
                }

                // Cek apakah pendidikan sudah ditambahkan
                const educationData = $("#educationData").val();
                let educationArray = [];
                try {
                    educationArray = JSON.parse(educationData || '[]');
                } catch (e) {
                    educationArray = [];
                }

                // Jika keahlian atau pendidikan belum ditambahkan, tampilkan pesan error dan tetap di step ini
                if (skillsArray.length === 0 || educationArray.length === 0) {
                    // Update pesan validasi untuk keahlian
                    if (skillsArray.length === 0) {
                        $("#skillsValidationMessage").html(
                            '<small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Minimal satu keahlian harus ditambahkan sebelum melanjutkan</small>'
                        );
                    }

                    // Update pesan validasi untuk pendidikan
                    if (educationArray.length === 0) {
                        $("#educationValidationMessage").html(
                            '<small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Minimal satu riwayat pendidikan harus ditambahkan sebelum melanjutkan</small>'
                        );
                    }

                    // Scroll ke bagian yang error
                    if (skillsArray.length === 0) {
                        $('html, body').animate({
                            scrollTop: $("#skillsTable").offset().top - 100
                        }, 500);
                    } else if (educationArray.length === 0) {
                        $('html, body').animate({
                            scrollTop: $("#educationTable").offset().top - 100
                        }, 500);
                    }

                    return false; // Mencegah perpindahan ke step selanjutnya
                }
            }

            // Validasi form pada step saat ini
            if (currentIndex < newIndex) {
                form.find('.body:eq(' + newIndex + ') label.error').remove();
                form.find('.body:eq(' + newIndex + ') .error').removeClass('error');
            }

            form.validate().settings.ignore = ":disabled,:hidden";
            return form.valid();
        },
        onFinishing: function(event, currentIndex) {
            var form = $(this);

            // Validasi khusus untuk step Jadwal & Tarif (index 2) sebelum submit
            if (currentIndex === 2) {
                // Cek apakah jadwal sudah ditambahkan
                const scheduleData = $("#scheduleData").val();
                let scheduleArray = [];
                try {
                    scheduleArray = JSON.parse(scheduleData || '[]');
                } catch (e) {
                    scheduleArray = [];
                }

                // Cek apakah tarif sudah ditambahkan
                const rateData = $("#rateData").val();
                let rateArray = [];
                try {
                    rateArray = JSON.parse(rateData || '[]');
                } catch (e) {
                    rateArray = [];
                }

                // Jika jadwal atau tarif belum ditambahkan, tampilkan pesan error dan tetap di step ini
                if (scheduleArray.length === 0 || rateArray.length === 0) {
                    // Update pesan validasi untuk jadwal
                    if (scheduleArray.length === 0) {
                        $("#scheduleValidationMessage").html(
                            '<small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Minimal satu jadwal harus ditambahkan sebelum mendaftar</small>'
                        );
                    }

                    // Update pesan validasi untuk tarif
                    if (rateArray.length === 0) {
                        $("#rateValidationMessage").html(
                            '<small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Minimal satu tarif harus ditambahkan sebelum mendaftar</small>'
                        );
                    }

                    // Scroll ke bagian yang error
                    if (scheduleArray.length === 0) {
                        $('html, body').animate({
                            scrollTop: $("#scheduleTable").offset().top - 100
                        }, 500);
                    } else if (rateArray.length === 0) {
                        $('html, body').animate({
                            scrollTop: $("#rateTable").offset().top - 100
                        }, 500);
                    }

                    return false; // Mencegah submit form
                }
            }

            form.validate().settings.ignore = ":disabled";
            return form.valid();
        },
        onFinished: function(event, currentIndex) {
            // Submit form saat selesai
            $(this).submit();
        },
        onStepChanged: function(event, currentIndex, priorIndex) {
            // Update status validasi saat step berubah
            if (currentIndex === 1) {
                // Update status validasi untuk keahlian dan pendidikan
                updateSkillsValidationStatus();
                updateEducationValidationStatus();

                // Jika ada data yang sudah ada, render ulang tabel
                renderSkillsTable();
                renderEducationTable();
            } else if (currentIndex === 2) {
                // Update status validasi untuk jadwal dan tarif
                updateScheduleValidationStatus();
                updateRateValidationStatus();

                // Jika ada data yang sudah ada, render ulang tabel
                renderScheduleTable();
                renderRateTable();
            }
        }
    }).validate({
        errorPlacement: function(error, element) {
            element.after(error);
        },
        rules: {
            fullName: {
                required: true,
                minlength: 3
            },
            whatsapp: {
                required: true,
                digits: true,
                minlength: 10,
                maxlength: 15
            },
            email: {
                required: true,
                email: true
            },
            gender: {
                required: true
            },
            province: {
                required: true,
                validProvince: true
            },
            city: {
                required: true,
                validCity: true
            },
            district: {
                required: true,
                validDistrict: true
            },
            village: {
                required: true,
                validVillage: true
            },
            address: {
                required: true,
                minlength: 10,
                maxlength: 500
            },
            selfDescription: {
                required: true
            },
            teachingExperience: {
                required: true,
                digits: true,
                min: 0,
                max: 50
            },
            skillsData: {
                validSkillsData: true
            },
            educationData: {
                validEducationData: true
            },
            scheduleData: {
                validScheduleData: true
            },
            rateData: {
                validRateData: true
            },
            password: {
                required: true,
                minlength: 6
            },
            confirmPassword: {
                required: true,
                equalTo: "#password"
            }
        },
        messages: {
            fullName: {
                required: "Nama lengkap wajib diisi",
                minlength: "Nama lengkap minimal 3 karakter"
            },
            whatsapp: {
                required: "Nomor WhatsApp wajib diisi",
                digits: "Nomor WhatsApp hanya boleh berisi angka",
                minlength: "Nomor WhatsApp minimal 10 digit",
                maxlength: "Nomor WhatsApp maksimal 15 digit"
            },
            email: {
                required: "Email wajib diisi",
                email: "Format email tidak valid"
            },
            gender: {
                required: "Jenis kelamin wajib dipilih"
            },
            province: {
                required: "Provinsi wajib dipilih",
                validProvince: "Provinsi yang dipilih tidak valid"
            },
            city: {
                required: "Kabupaten/Kota wajib dipilih",
                validCity: "Kabupaten/Kota yang dipilih tidak valid atau tidak sesuai dengan provinsi"
            },
            district: {
                required: "Kecamatan wajib dipilih",
                validDistrict: "Kecamatan yang dipilih tidak valid atau tidak sesuai dengan kabupaten/kota"
            },
            village: {
                required: "Kelurahan/Desa wajib dipilih",
                validVillage: "Kelurahan/Desa yang dipilih tidak valid atau tidak sesuai dengan kecamatan"
            },
            address: {
                required: "Alamat lengkap wajib diisi",
                minlength: "Alamat terlalu pendek, minimal 10 karakter",
                maxlength: "Alamat terlalu panjang, maksimal 500 karakter"
            },
            selfDescription: {
                required: "Deskripsi diri wajib diisi"
            },
            teachingExperience: {
                required: "Pengalaman mengajar wajib diisi",
                digits: "Pengalaman mengajar harus berupa angka",
                min: "Pengalaman mengajar tidak boleh negatif",
                max: "Pengalaman mengajar tidak boleh lebih dari 50 tahun"
            },
            skillsData: {
                validSkillsData: "Minimal satu keahlian harus ditambahkan"
            },
            educationData: {
                validEducationData: "Minimal satu riwayat pendidikan harus ditambahkan"
            },
            scheduleData: {
                validScheduleData: "Minimal satu jadwal harus ditambahkan"
            },
            rateData: {
                validRateData: "Minimal satu tarif harus ditambahkan"
            },
            password: {
                required: "Password wajib diisi",
                minlength: "Password minimal 6 karakter"
            },
            confirmPassword: {
                required: "Konfirmasi password wajib diisi",
                equalTo: "Password konfirmasi harus sama dengan password"
            }
        }
    });



    // Tambahkan validasi real-time untuk field pengalaman mengajar dan deskripsi diri
    $(document).ready(function() {
        // Validasi real-time untuk deskripsi diri - hanya cek apakah diisi
        $("#selfDescription").on("input", function() {
            const description = $(this).val().trim();
            const $feedback = $(this).siblings(".text-danger, .text-success");

            // Hapus feedback message yang ada
            $feedback.remove();
            $(this).removeClass("is-invalid");

            if (description.length > 0) {
                $(this).removeClass("is-invalid");
                $(this).after('<small class="text-success">Deskripsi diri sudah diisi</small>');
            }
        });

        // Validasi real-time untuk pengalaman mengajar - hanya cek apakah diisi
        $("#teachingExperience").on("input", function() {
            const experience = $(this).val();
            const $feedback = $(this).siblings(".text-danger, .text-success");

            // Hapus feedback message yang ada
            $feedback.remove();
            $(this).removeClass("is-invalid");

            if (experience.length > 0) {
                $(this).removeClass("is-invalid");
                $(this).after('<small class="text-success">Pengalaman mengajar sudah diisi</small>');
            }
        });

        // Validasi untuk memastikan minimal ada satu keahlian
        function validateMinimumSkills() {
            const skillsCount = skills.length;
            const $skillsError = $("#skillsData").siblings(".text-danger");

            $skillsError.remove();

            if (skillsCount === 0) {
                $("#skillsData").after(
                    '<small class="text-danger">Minimal satu keahlian harus ditambahkan</small>');
                return false;
            } else {
                $("#skillsData").after('<small class="text-success">' + skillsCount +
                    ' keahlian telah ditambahkan</small>');
                return true;
            }
        }

        // Validasi untuk memastikan minimal ada satu pendidikan
        function validateMinimumEducation() {
            const educationCount = educations.length;
            const $educationError = $("#educationData").siblings(".text-danger");

            $educationError.remove();

            if (educationCount === 0) {
                $("#educationData").after(
                    '<small class="text-danger">Minimal satu riwayat pendidikan harus ditambahkan</small>');
                return false;
            } else {
                $("#educationData").after('<small class="text-success">' + educationCount +
                    ' riwayat pendidikan telah ditambahkan</small>');
                return true;
            }
        }

        // Panggil validasi setiap kali data keahlian atau pendidikan berubah
        const originalUpdateSkillsData = updateSkillsData;
        updateSkillsData = function() {
            originalUpdateSkillsData();
            validateMinimumSkills();
        };

        const originalUpdateEducationData = updateEducationData;
        updateEducationData = function() {
            originalUpdateEducationData();
            validateMinimumEducation();
        };

        // Validasi awal
        validateMinimumSkills();
        validateMinimumEducation();
    });

    setupEventListeners();
    </script>

    <script>
    $(document).ready(function() {
        // Fungsi untuk menampilkan tab berdasarkan error validasi
        function showTabWithErrors() {
            // Cek apakah ada error validasi
            const hasErrors = $(".text-danger").length > 0;

            if (hasErrors) {
                // Cari tab dengan error
                let tabWithError = 0;

                // Cek error di tab Data Utama (tab 0)
                if ($("#fullName").siblings(".text-danger").length > 0 ||
                    $("#whatsapp").siblings(".text-danger").length > 0 ||
                    $("#email").siblings(".text-danger").length > 0 ||
                    $("#province").siblings(".text-danger").length > 0 ||
                    $("#city").siblings(".text-danger").length > 0 ||
                    $("#district").siblings(".text-danger").length > 0 ||
                    $("#village").siblings(".text-danger").length > 0 ||
                    $("#address").siblings(".text-danger").length > 0) {
                    tabWithError = 0;
                }
                // Cek error di tab Data Diri (tab 1)
                else if ($("#selfDescription").siblings(".text-danger").length > 0 ||
                    $("#teachingExperience").siblings(".text-danger").length > 0 ||
                    $("#skillsData").siblings(".text-danger").length > 0 ||
                    $("#educationData").siblings(".text-danger").length > 0) {
                    tabWithError = 1;
                }
                // Cek error di tab Jadwal & Tarif (tab 2)
                else if ($("#scheduleData").siblings(".text-danger").length > 0 ||
                    $("#rateData").siblings(".text-danger").length > 0) {
                    tabWithError = 2;
                }
                // Cek error di tab Keamanan (tab 3)
                else if ($("#password").siblings(".text-danger").length > 0 ||
                    $("#confirmPassword").siblings(".text-danger").length > 0) {
                    tabWithError = 3;
                }

                // Tampilkan tab dengan error
                setTimeout(function() {
                    $(".validation-wizard").steps("setStep", tabWithError);

                    // Scroll ke error pertama dalam tab
                    const $firstError = $(".validation-wizard .body:eq(" + tabWithError + ")").find(
                        ".text-danger:first");
                    if ($firstError.length > 0) {
                        $('html, body').animate({
                            scrollTop: $firstError.offset().top - 100
                        }, 500);
                    }
                }, 300);
            } else {
                // Jika tidak ada error, cek apakah ada active tab dari session
                const activeTab = $("#activeTab").val();
                if (activeTab && activeTab !== '0') {
                    setTimeout(function() {
                        $(".validation-wizard").steps("setStep", parseInt(activeTab));
                    }, 300);
                }
            }
        }

        // Tambahkan event listener untuk menyimpan tab aktif saat pindah tab
        $(".validation-wizard").on("leaveStep", function(e, anchorObject, currentIndex, newIndex) {
            $("#activeTab").val(newIndex);
        });

        // Panggil fungsi untuk menampilkan tab dengan error setelah wizard diinisialisasi
        setTimeout(showTabWithErrors, 500);

        // ===== CRUD KEAHLIAN =====
        let skills = [];
        try {
            skills = JSON.parse($("#skillsData").val());
        } catch (e) {
            skills = [];
        }

        // Fungsi untuk menampilkan data keahlian dalam tabel
        function renderSkillsTable() {
            const $tbody = $("#skillsTable tbody");
            $tbody.empty();

            if (skills.length === 0) {
                $tbody.append('<tr><td colspan="3" class="text-center">Belum ada data keahlian</td></tr>');
            } else {
                $.each(skills, function(index, skill) {
                    $tbody.append(`
                            <tr>
                                <td>${skill.name}</td>
                                <td>${skill.level}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-danger delete-skill" data-index="${index}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `);
                });

                // Tambahkan event listener untuk tombol hapus
                $(".delete-skill").on("click", function() {
                    const index = $(this).data("index");
                    skills.splice(index, 1);
                    updateSkillsData();
                    renderSkillsTable();
                });
            }
        }

        // Fungsi untuk memperbarui input hidden dengan data keahlian
        function updateSkillsData() {
            $("#skillsData").val(JSON.stringify(skills));
        }

        // Tampilkan data keahlian yang sudah ada
        renderSkillsTable();

        // Variabel untuk menyimpan data form keahlian sementara
        let tempSkillData = {
            name: "",
            level: ""
        };

        // Event listener untuk tombol tambah keahlian
        $("#addSkill").on("click", function() {
            // Buat modal untuk form tambah keahlian
            const modalHtml = `
                    <div class="modal fade" id="skillModal" tabindex="-1" aria-labelledby="skillModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="skillModalLabel">Tambah Keahlian</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="skillName" class="form-label">Nama Keahlian</label>
                                        <input type="text" class="form-control" id="skillName" placeholder="Contoh: Tahfidz, Tajwid, Qiroah" autocomplete="off" required>
                                        <div class="invalid-feedback" id="skillNameFeedback"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="skillLevel" class="form-label">Tingkat Kemampuan</label>
                                        <select class="form-select" id="skillLevel" autocomplete="off" required>
                                            <option value="" selected>Pilih Tingkat</option>
                                            <option value="Pemula">Pemula</option>
                                            <option value="Menengah">Menengah</option>
                                            <option value="Mahir">Mahir</option>
                                            <option value="Ahli">Ahli</option>
                                        </select>
                                        <div class="invalid-feedback" id="skillLevelFeedback"></div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                    <button type="button" class="btn btn-primary" id="saveSkill">Simpan</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

            // Hapus modal lama jika ada
            $("#skillModal").remove();

            // Tambahkan modal baru ke body
            $("body").append(modalHtml);

            // Tampilkan modal
            const skillModal = new bootstrap.Modal($("#skillModal")[0]);
            skillModal.show();

            // Isi form dengan data sementara (auto-fill)
            $("#skillName").val(tempSkillData.name);
            $("#skillLevel").val(tempSkillData.level);

            // Event listener untuk input fields (simpan nilai sementara dan validasi real-time)
            $("#skillName").on("input", function() {
                tempSkillData.name = $(this).val();

                // Validasi real-time untuk nama keahlian
                const name = $(this).val().trim();
                $(this).removeClass("is-invalid");
                $("#skillNameFeedback").text("");

                if (name.length > 0) {
                    if (name.length < 3) {
                        $(this).addClass("is-invalid");
                        $("#skillNameFeedback").text("Nama keahlian minimal 3 karakter");
                    } else if (name.length > 100) {
                        $(this).addClass("is-invalid");
                        $("#skillNameFeedback").text("Nama keahlian maksimal 100 karakter");
                    } else {
                        const skillExists = skills.some(skill =>
                            skill.name.toLowerCase() === name.toLowerCase()
                        );
                        if (skillExists) {
                            $(this).addClass("is-invalid");
                            $("#skillNameFeedback").text(
                                "Keahlian ini sudah ada, tidak boleh duplikat");
                        }
                    }
                }
            });

            $("#skillLevel").on("change", function() {
                tempSkillData.level = $(this).val();

                // Reset validasi saat level dipilih
                $(this).removeClass("is-invalid");
                $("#skillLevelFeedback").text("");
            });

            // Event listener untuk tombol simpan
            $("#saveSkill").on("click", function() {
                // Reset validasi
                $("#skillName").removeClass("is-invalid");
                $("#skillLevel").removeClass("is-invalid");
                $("#skillNameFeedback").text("");
                $("#skillLevelFeedback").text("");

                let isValid = true;
                const name = $("#skillName").val();
                const level = $("#skillLevel").val();

                // Validasi nama keahlian
                if (!name || name.trim() === "") {
                    $("#skillName").addClass("is-invalid");
                    $("#skillNameFeedback").text("Nama keahlian harus diisi");
                    isValid = false;
                } else if (name.trim().length < 3) {
                    $("#skillName").addClass("is-invalid");
                    $("#skillNameFeedback").text("Nama keahlian minimal 3 karakter");
                    isValid = false;
                } else if (name.trim().length > 100) {
                    $("#skillName").addClass("is-invalid");
                    $("#skillNameFeedback").text("Nama keahlian maksimal 100 karakter");
                    isValid = false;
                } else {
                    // Cek duplikasi keahlian
                    const skillExists = skills.some(skill =>
                        skill.name.toLowerCase() === name.trim().toLowerCase()
                    );
                    if (skillExists) {
                        $("#skillName").addClass("is-invalid");
                        $("#skillNameFeedback").text(
                            "Keahlian ini sudah ada, tidak boleh duplikat");
                        isValid = false;
                    }
                }

                // Validasi tingkat kemampuan
                if (!level) {
                    $("#skillLevel").addClass("is-invalid");
                    $("#skillLevelFeedback").text("Tingkat kemampuan harus dipilih");
                    isValid = false;
                }

                // Validasi maksimal jumlah keahlian
                if (skills.length >= 20) {
                    $("#skillName").addClass("is-invalid");
                    $("#skillNameFeedback").text("Maksimal 20 keahlian yang dapat ditambahkan");
                    isValid = false;
                }

                // Jika validasi gagal, hentikan proses
                if (!isValid) {
                    return;
                }

                // Tambahkan data keahlian baru
                skills.push({
                    name,
                    level
                });

                // Reset data sementara
                tempSkillData = {
                    name: "",
                    level: ""
                };

                // Perbarui data dan tampilan
                updateSkillsData();
                renderSkillsTable();

                // Tutup modal
                skillModal.hide();
            });
        });

        // ===== CRUD PENDIDIKAN =====
        let educations = [];
        try {
            educations = JSON.parse($("#educationData").val());
        } catch (e) {
            educations = [];
        }

        // Fungsi untuk menampilkan data pendidikan dalam tabel
        function renderEducationTable() {
            const $tbody = $("#educationTable tbody");
            $tbody.empty();

            if (educations.length === 0) {
                $tbody.append('<tr><td colspan="4" class="text-center">Belum ada data pendidikan</td></tr>');
            } else {
                $.each(educations, function(index, edu) {
                    $tbody.append(`
                            <tr>
                                <td>${edu.level}</td>
                                <td>${edu.institution}</td>
                                <td>${edu.graduationYear}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-danger delete-edu" data-index="${index}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `);
                });

                // Tambahkan event listener untuk tombol hapus
                $(".delete-edu").on("click", function() {
                    const index = $(this).data("index");
                    educations.splice(index, 1);
                    updateEducationData();
                    renderEducationTable();
                });
            }
        }

        // Fungsi untuk memperbarui input hidden dengan data pendidikan
        function updateEducationData() {
            $("#educationData").val(JSON.stringify(educations));
            // Trigger validasi ulang untuk field educationData
            $("#educationData").valid();
        }

        // Tampilkan data pendidikan yang sudah ada
        renderEducationTable();

        // Variabel untuk menyimpan data form pendidikan sementara
        let tempEduData = {
            level: "",
            institution: "",
            graduationYear: ""
        };

        // Event listener untuk tombol tambah pendidikan
        $("#addEducation").on("click", function() {
            // Buat modal untuk form tambah pendidikan
            const modalHtml = `
                    <div class="modal fade" id="educationModal" tabindex="-1" aria-labelledby="educationModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="educationModalLabel">Tambah Riwayat Pendidikan</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="eduLevel" class="form-label">Jenjang Pendidikan</label>
                                        <select class="form-select" id="eduLevel" autocomplete="off" required>
                                            <option value="" selected>Pilih Jenjang</option>
                                            <option value="SD/MI">SD/MI</option>
                                            <option value="SMP/MTs">SMP/MTs</option>
                                            <option value="SMA/MA/SMK">SMA/MA/SMK</option>
                                            <option value="D1">D1</option>
                                            <option value="D2">D2</option>
                                            <option value="D3">D3</option>
                                            <option value="D4/S1">D4/S1</option>
                                            <option value="S2">S2</option>
                                            <option value="S3">S3</option>
                                            <option value="Pesantren">Pesantren</option>
                                            <option value="Lainnya">Lainnya</option>
                                        </select>
                                        <div class="invalid-feedback" id="eduLevelFeedback"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="eduInstitution" class="form-label">Nama Institusi</label>
                                        <input type="text" class="form-control" id="eduInstitution" placeholder="Contoh: Universitas Indonesia" autocomplete="off" required>
                                        <div class="invalid-feedback" id="eduInstitutionFeedback"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="eduYear" class="form-label">Tahun Lulus</label>
                                        <input type="number" class="form-control" id="eduYear" min="1950" max="${new Date().getFullYear()}" placeholder="Contoh: 2020" autocomplete="off" required>
                                        <div class="invalid-feedback" id="eduYearFeedback"></div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                    <button type="button" class="btn btn-primary" id="saveEducation">Simpan</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

            // Hapus modal lama jika ada
            $("#educationModal").remove();

            // Tambahkan modal baru ke body
            $("body").append(modalHtml);

            // Tampilkan modal
            const educationModal = new bootstrap.Modal($("#educationModal")[0]);
            educationModal.show();

            // Isi form dengan data sementara (auto-fill)
            $("#eduLevel").val(tempEduData.level);
            $("#eduInstitution").val(tempEduData.institution);
            $("#eduYear").val(tempEduData.graduationYear);

            // Event listener untuk input fields (simpan nilai sementara dan validasi real-time)
            $("#eduLevel").on("change", function() {
                tempEduData.level = $(this).val();

                // Reset validasi saat level dipilih
                $(this).removeClass("is-invalid");
                $("#eduLevelFeedback").text("");
            });

            $("#eduInstitution").on("input", function() {
                tempEduData.institution = $(this).val();

                // Validasi real-time untuk nama institusi
                const institution = $(this).val().trim();
                const level = $("#eduLevel").val();
                $(this).removeClass("is-invalid");
                $("#eduInstitutionFeedback").text("");

                if (institution.length > 0) {
                    if (institution.length < 3) {
                        $(this).addClass("is-invalid");
                        $("#eduInstitutionFeedback").text("Nama institusi minimal 3 karakter");
                    } else if (institution.length > 200) {
                        $(this).addClass("is-invalid");
                        $("#eduInstitutionFeedback").text(
                            "Nama institusi maksimal 200 karakter");
                    } else if (level) {
                        const educationExists = educations.some(edu =>
                            edu.level === level && edu.institution.toLowerCase() ===
                            institution.toLowerCase()
                        );
                        if (educationExists) {
                            $(this).addClass("is-invalid");
                            $("#eduInstitutionFeedback").text(
                                "Kombinasi jenjang dan institusi ini sudah ada");
                        }
                    }
                }
            });

            $("#eduYear").on("input", function() {
                tempEduData.graduationYear = $(this).val();

                // Validasi real-time untuk tahun lulus
                const year = parseInt($(this).val());
                const currentYear = new Date().getFullYear();
                $(this).removeClass("is-invalid");
                $("#eduYearFeedback").text("");

                if ($(this).val().length > 0) {
                    if (isNaN(year)) {
                        $(this).addClass("is-invalid");
                        $("#eduYearFeedback").text("Tahun lulus harus berupa angka");
                    } else if (year < 1950) {
                        $(this).addClass("is-invalid");
                        $("#eduYearFeedback").text("Tahun lulus tidak boleh kurang dari 1950");
                    } else if (year > currentYear) {
                        $(this).addClass("is-invalid");
                        $("#eduYearFeedback").text(
                            "Tahun lulus tidak boleh lebih dari tahun sekarang");
                    }
                }
            });

            // Event listener untuk tombol simpan
            $("#saveEducation").on("click", function() {
                // Reset validasi
                $("#eduLevel").removeClass("is-invalid");
                $("#eduInstitution").removeClass("is-invalid");
                $("#eduYear").removeClass("is-invalid");
                $("#eduLevelFeedback").text("");
                $("#eduInstitutionFeedback").text("");
                $("#eduYearFeedback").text("");

                let isValid = true;
                const level = $("#eduLevel").val();
                const institution = $("#eduInstitution").val();
                const graduationYear = $("#eduYear").val();
                const currentYear = new Date().getFullYear();

                // Validasi jenjang pendidikan
                if (!level) {
                    $("#eduLevel").addClass("is-invalid");
                    $("#eduLevelFeedback").text("Jenjang pendidikan harus dipilih");
                    isValid = false;
                }

                // Validasi nama institusi
                if (!institution || institution.trim() === "") {
                    $("#eduInstitution").addClass("is-invalid");
                    $("#eduInstitutionFeedback").text("Nama institusi harus diisi");
                    isValid = false;
                } else if (institution.trim().length < 3) {
                    $("#eduInstitution").addClass("is-invalid");
                    $("#eduInstitutionFeedback").text("Nama institusi minimal 3 karakter");
                    isValid = false;
                } else if (institution.trim().length > 200) {
                    $("#eduInstitution").addClass("is-invalid");
                    $("#eduInstitutionFeedback").text("Nama institusi maksimal 200 karakter");
                    isValid = false;
                } else {
                    // Cek duplikasi kombinasi jenjang dan institusi
                    const educationExists = educations.some(edu =>
                        edu.level === level && edu.institution.toLowerCase() === institution
                        .trim().toLowerCase()
                    );
                    if (educationExists) {
                        $("#eduInstitution").addClass("is-invalid");
                        $("#eduInstitutionFeedback").text(
                            "Kombinasi jenjang dan institusi ini sudah ada");
                        isValid = false;
                    }
                }

                // Validasi tahun lulus
                if (!graduationYear) {
                    $("#eduYear").addClass("is-invalid");
                    $("#eduYearFeedback").text("Tahun lulus harus diisi");
                    isValid = false;
                } else if (graduationYear < 1950 || graduationYear > currentYear) {
                    $("#eduYear").addClass("is-invalid");
                    $("#eduYearFeedback").text(
                        `Tahun lulus harus antara 1950 dan ${currentYear}`);
                    isValid = false;
                }

                // Validasi maksimal jumlah pendidikan
                if (educations.length >= 10) {
                    $("#eduLevel").addClass("is-invalid");
                    $("#eduLevelFeedback").text(
                        "Maksimal 10 riwayat pendidikan yang dapat ditambahkan");
                    isValid = false;
                }

                // Jika validasi gagal, hentikan proses
                if (!isValid) {
                    return;
                }

                // Tambahkan data pendidikan baru
                educations.push({
                    level,
                    institution,
                    graduationYear
                });

                // Reset data sementara
                tempEduData = {
                    level: "",
                    institution: "",
                    graduationYear: ""
                };

                // Perbarui data dan tampilan
                updateEducationData();
                renderEducationTable();

                // Tutup modal
                educationModal.hide();
            });
        });

        // ===== CRUD JADWAL =====
        let schedules = [];
        try {
            schedules = JSON.parse($("#scheduleData").val());
        } catch (e) {
            schedules = [];
        }

        // Fungsi untuk menampilkan data jadwal dalam tabel
        function renderScheduleTable() {
            const $tbody = $("#scheduleTable tbody");
            $tbody.empty();

            if (schedules.length === 0) {
                $tbody.append('<tr><td colspan="4" class="text-center">Belum ada data jadwal</td></tr>');
            } else {
                $.each(schedules, function(index, schedule) {
                    $tbody.append(`
                            <tr>
                                <td>${schedule.day}</td>
                                <td>${schedule.startTime}</td>
                                <td>${schedule.endTime}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-danger delete-schedule" data-index="${index}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `);
                });

                // Tambahkan event listener untuk tombol hapus
                $(".delete-schedule").on("click", function() {
                    const index = $(this).data("index");
                    schedules.splice(index, 1);
                    updateScheduleData();
                    renderScheduleTable();
                });
            }
        }

        // Fungsi untuk memperbarui input hidden dengan data jadwal
        function updateScheduleData() {
            $("#scheduleData").val(JSON.stringify(schedules));
        }

        // Tampilkan data jadwal yang sudah ada
        renderScheduleTable();

        // Variabel untuk menyimpan data form jadwal sementara
        let tempScheduleData = {
            day: "",
            startTime: "",
            endTime: ""
        };

        // Event listener untuk tombol tambah jadwal
        $("#addSchedule").on("click", function() {
            // Buat modal untuk form tambah jadwal
            const modalHtml = `
                    <div class="modal fade" id="scheduleModal" tabindex="-1" aria-labelledby="scheduleModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="scheduleModalLabel">Tambah Jadwal</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="scheduleDay" class="form-label">Hari</label>
                                        <select class="form-select" id="scheduleDay" autocomplete="off" required>
                                            <option value="" selected>Pilih Hari</option>
                                            <option value="Senin">Senin</option>
                                            <option value="Selasa">Selasa</option>
                                            <option value="Rabu">Rabu</option>
                                            <option value="Kamis">Kamis</option>
                                            <option value="Jumat">Jumat</option>
                                            <option value="Sabtu">Sabtu</option>
                                            <option value="Minggu">Minggu</option>
                                        </select>
                                        <div class="invalid-feedback" id="scheduleDayFeedback"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="startTime" class="form-label">Jam Mulai</label>
                                        <input type="time" class="form-control" id="startTime" autocomplete="off" required>
                                        <div class="invalid-feedback" id="startTimeFeedback"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="endTime" class="form-label">Jam Selesai</label>
                                        <input type="time" class="form-control" id="endTime" autocomplete="off" required>
                                        <div class="invalid-feedback" id="endTimeFeedback"></div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                    <button type="button" class="btn btn-primary" id="saveSchedule">Simpan</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

            // Hapus modal lama jika ada
            $("#scheduleModal").remove();

            // Tambahkan modal baru ke body
            $("body").append(modalHtml);

            // Tampilkan modal
            const scheduleModal = new bootstrap.Modal($("#scheduleModal")[0]);
            scheduleModal.show();

            // Isi form dengan data sementara (auto-fill)
            $("#scheduleDay").val(tempScheduleData.day);
            $("#startTime").val(tempScheduleData.startTime);
            $("#endTime").val(tempScheduleData.endTime);

            // Event listener untuk input fields (simpan nilai sementara)
            $("#scheduleDay").on("change", function() {
                tempScheduleData.day = $(this).val();
            });

            $("#startTime").on("input", function() {
                tempScheduleData.startTime = $(this).val();
            });

            $("#endTime").on("input", function() {
                tempScheduleData.endTime = $(this).val();
            });

            // Event listener untuk tombol simpan
            $("#saveSchedule").on("click", function() {
                // Reset validasi
                $("#scheduleDay").removeClass("is-invalid");
                $("#startTime").removeClass("is-invalid");
                $("#endTime").removeClass("is-invalid");
                $("#scheduleDayFeedback").text("");
                $("#startTimeFeedback").text("");
                $("#endTimeFeedback").text("");

                let isValid = true;
                const day = $("#scheduleDay").val();
                const startTime = $("#startTime").val();
                const endTime = $("#endTime").val();

                // Validasi hari
                if (!day) {
                    $("#scheduleDay").addClass("is-invalid");
                    $("#scheduleDayFeedback").text("Hari harus dipilih");
                    isValid = false;
                }

                // Validasi jam mulai
                if (!startTime) {
                    $("#startTime").addClass("is-invalid");
                    $("#startTimeFeedback").text("Jam mulai harus diisi");
                    isValid = false;
                }

                // Validasi jam selesai
                if (!endTime) {
                    $("#endTime").addClass("is-invalid");
                    $("#endTimeFeedback").text("Jam selesai harus diisi");
                    isValid = false;
                } else if (startTime && endTime && startTime >= endTime) {
                    $("#endTime").addClass("is-invalid");
                    $("#endTimeFeedback").text("Jam selesai harus lebih besar dari jam mulai");
                    isValid = false;
                }

                // Jika validasi gagal, hentikan proses
                if (!isValid) {
                    return;
                }

                // Tambahkan data jadwal baru
                schedules.push({
                    day,
                    startTime,
                    endTime
                });

                // Reset data sementara
                tempScheduleData = {
                    day: "",
                    startTime: "",
                    endTime: ""
                };

                // Perbarui data dan tampilan
                updateScheduleData();
                renderScheduleTable();

                // Tutup modal
                scheduleModal.hide();
            });
        });

        // ===== CRUD TARIF =====
        let rates = [];
        try {
            rates = JSON.parse($("#rateData").val());
        } catch (e) {
            rates = [];
        }

        // Fungsi untuk menampilkan data tarif dalam tabel
        function renderRateTable() {
            const $tbody = $("#rateTable tbody");
            $tbody.empty();

            if (rates.length === 0) {
                $tbody.append('<tr><td colspan="4" class="text-center">Belum ada data tarif</td></tr>');
            } else {
                $.each(rates, function(index, rate) {
                    $tbody.append(`
                            <tr>
                                <td>${rate.service}</td>
                                <td>${rate.duration}</td>
                                <td>${new Intl.NumberFormat('id-ID').format(rate.price)}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-danger delete-rate" data-index="${index}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `);
                });

                // Tambahkan event listener untuk tombol hapus
                $(".delete-rate").on("click", function() {
                    const index = $(this).data("index");
                    rates.splice(index, 1);
                    updateRateData();
                    renderRateTable();
                });
            }
        }

        // Fungsi untuk memperbarui input hidden dengan data tarif
        function updateRateData() {
            $("#rateData").val(JSON.stringify(rates));
        }

        // Tampilkan data tarif yang sudah ada
        renderRateTable();

        // Event listener untuk tombol tambah tarif
        $("#addRate").on("click", function() {
            // Buat modal untuk form tambah tarif
            const modalHtml = `
                    <div class="modal fade" id="rateModal" tabindex="-1" aria-labelledby="rateModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="rateModalLabel">Tambah Tarif</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="service" class="form-label">Jenis Layanan</label>
                                        <input type="text" class="form-control" id="service" placeholder="Contoh: Privat, Kelompok" autocomplete="off" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="duration" class="form-label">Durasi</label>
                                        <input type="text" class="form-control" id="duration" placeholder="Contoh: 60 menit, 2 jam" autocomplete="off" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="price" class="form-label">Tarif (Rp)</label>
                                        <input type="number" class="form-control" id="price" placeholder="Contoh: 100000" autocomplete="off" required>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                    <button type="button" class="btn btn-primary" id="saveRate">Simpan</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

            // Tambahkan modal ke body jika belum ada
            if ($("#rateModal").length === 0) {
                $("body").append(modalHtml);
            }

            // Tampilkan modal
            const rateModal = new bootstrap.Modal($("#rateModal")[0]);
            rateModal.show();

            // Event listener untuk tombol simpan
            $("#saveRate").on("click", function() {
                const service = $("#service").val();
                const duration = $("#duration").val();
                const price = $("#price").val();

                // Validasi input
                if (!service || !duration || !price) {
                    alert('Semua field harus diisi!');
                    return;
                }

                // Tambahkan data tarif baru
                rates.push({
                    service,
                    duration,
                    price
                });

                // Perbarui data dan tampilan
                updateRateData();
                renderRateTable();

                // Tutup modal
                rateModal.hide();
            });
        });

        setupEventListeners();
    });


    // Pastikan nilai gender tetap ada saat form disubmit
    $(document).ready(function() {
        // Tambahkan event listener untuk form submit
        $(".validation-wizard").on("submit", function(e) {
            // Cek apakah ada gender yang dipilih
            var selectedGender = $("input[name='gender']:checked").val();

            // Jika tidak ada, coba ambil dari hidden input
            if (!selectedGender) {
                selectedGender = $("#gender_hidden").val();

                // Jika ada nilai di hidden input, tambahkan sebagai input hidden baru
                if (selectedGender) {
                    // Tambahkan input hidden baru dengan nama 'gender_final'
                    $(this).append('<input type="hidden" name="gender_final" value="' + selectedGender +
                        '">');
                    console.log("Added gender_final hidden input with value:", selectedGender);
                } else {
                    // Jika masih tidak ada, tampilkan pesan error
                    e.preventDefault();
                    alert("Pilih jenis kelamin terlebih dahulu");
                    return false;
                }
            }

            // Log untuk debugging
            console.log("Form submitted with gender:", selectedGender);
        });
    });
    </script>
</body>

</html>