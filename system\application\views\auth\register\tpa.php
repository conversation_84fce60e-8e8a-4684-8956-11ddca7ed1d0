<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <!--  Title -->
    <title>Daftar sebagai <?= ucfirst($type) ?> - <PERSON><PERSON><PERSON> Go</title>
    <!--  Required Meta Tag -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="handheldfriendly" content="true" />
    <meta name="MobileOptimized" content="width" />
    <meta name="description" content="Daftar Ngaji Go" />
    <meta name="author" content="" />
    <meta name="keywords" content="Daftar" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--  Favicon -->
    <link rel="shortcut icon" type="image/png" href="<?php echo base_url('assets/images/logos/favicon.png'); ?>" />
    <!-- Core Css -->
    <link id="themeColors" rel="stylesheet" href="<?php echo base_url('assets/css/style.min.css'); ?>" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
</head>


<!-- Preloader -->
<div class="preloader">
    <img src="<?php echo base_url('assets/images/logos/favicon.png'); ?>" alt="loader" class="lds-ripple img-fluid" />
</div>
<!--  Body Wrapper -->
<div class="page-wrapper" id="main-wrapper" data-layout="vertical" data-sidebartype="full" data-sidebar-position="fixed"
    data-header-position="fixed">
    <div
        class="position-relative overflow-hidden radial-gradient min-vh-100 d-flex align-items-center justify-content-center p-5">
        <div class="d-flex align-items-center justify-content-center w-100">
            <div class="row justify-content-center w-100">
                <div class="col-md-12 col-lg-10 col-xxl-8">
                    <div class="card mb-0">
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-sm-6">
                                    <h2 class="mb-3 fs-7 fw-bolder"><?php echo $title; ?></h2>
                                </div>

                                <div class="col-sm-6 text-end">
                                    <div class="btn-group w-100" role="group">
                                        <a href="<?php echo site_url('auth/register/member'); ?>"
                                            class="btn <?php echo ($type == 'member') ? 'btn-primary' : 'btn-outline-primary'; ?>">Member</a>
                                        <a href="<?php echo site_url('auth/register/guru'); ?>"
                                            class="btn <?php echo ($type == 'guru') ? 'btn-primary' : 'btn-outline-primary'; ?>">Guru</a>
                                        <a href="<?php echo site_url('auth/register/tpa'); ?>"
                                            class="btn <?php echo ($type == 'tpa') ? 'btn-primary' : 'btn-outline-primary'; ?>">TPA</a>
                                    </div>
                                </div>
                            </div>
                            <p class="mb-4">Silahkan lengkapi data berikut untuk mendaftar sebagai
                                <?php echo $type; ?>. <small class="text-danger">* Wajib diisi</small></p>

                            <?php if (isset($error)): ?>
                            <?php echo $error; ?>
                            <?php endif; ?>

                            <?php if ($this->session->flashdata('message')): ?>
                            <?php echo $this->session->flashdata('message'); ?>
                            <?php endif; ?>

                            <?php echo form_open_multipart('auth/register/' . $type, ['class' => 'validation-wizard wizard-circle']); ?>

                            <!-- Step 1: Informasi Dasar TPA -->
                            <h6>Informasi Dasar</h6>
                            <section>
                                <div class="card card-body">
                                    <!-- Informasi TPA -->
                                    <div class="mb-3">
                                        <label for="tpaName" class="form-label">Nama TPA <span
                                                class="text-danger">*</span></label>
                                        <input type="text" class="form-control required" id="tpaName" name="tpaName"
                                            value="<?php echo set_value('tpaName'); ?>">
                                        <?php echo form_error('tpaName', '<small class="text-danger">', '</small>'); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="tpaDescription" class="form-label">Deskripsi TPA <span
                                                class="text-danger">*</span></label>
                                        <textarea class="form-control required" id="tpaDescription"
                                            name="tpaDescription" rows="3"
                                            placeholder="Deskripsi singkat tentang TPA"><?php echo set_value('tpaDescription'); ?></textarea>
                                        <?php echo form_error('tpaDescription', '<small class="text-danger">', '</small>'); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="tpaPhoto" class="form-label">Foto TPA <span
                                                class="text-danger">*</span></label>
                                        <input type="file" class="form-control required" id="tpaPhoto" name="tpaPhoto"
                                            accept="image/*">
                                        <small class="text-muted">Format: JPG, PNG. Maks: 2MB</small>
                                        <?php echo isset($error_tpaPhoto) ? '<small class="text-danger">' . $error_tpaPhoto . '</small>' : ''; ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="whatsapp" class="form-label">Nomor WhatsApp TPA <span
                                                class="text-danger">*</span></label>
                                        <input type="text" class="form-control required" id="whatsapp" name="whatsapp"
                                            inputmode="numeric" pattern="[0-9]*"
                                            value="<?php echo set_value('whatsapp'); ?>">

                                        <?php echo form_error('whatsapp', '<small class="text-danger">', '</small>'); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email TPA <span
                                                class="text-danger">*</span></label>
                                        <input type="email" class="form-control required" id="email" name="email"
                                            value="<?php echo set_value('email'); ?>">
                                        <?php echo form_error('email', '<small class="text-danger">', '</small>'); ?>
                                    </div>



                                    <div class="mb-3">
                                        <label for="establishedYear" class="form-label">Tahun Berdiri TPA <span
                                                class="text-danger">*</span></label>
                                        <input type="number" class="form-control required" id="establishedYear"
                                            name="establishedYear" min="1900" max="<?php echo date('Y'); ?>"
                                            value="<?php echo set_value('establishedYear'); ?>">
                                        <?php echo form_error('establishedYear', '<small class="text-danger">', '</small>'); ?>
                                    </div>

                                    <!-- Alamat TPA -->
                                    <div class="mb-3">
                                        <label class="form-label">Alamat TPA <span class="text-danger">*</span></label>
                                        <div class="card p-3 border">
                                            <div class="mb-3">
                                                <label for="province" class="form-label">Provinsi <span
                                                        class="text-danger">*</span></label>
                                                <select class="form-select required" id="province" name="province">
                                                    <option value="" selected>Pilih Provinsi</option>
                                                    <?php foreach ($provinces as $province): ?>
                                                    <option value="<?php echo $province->id; ?>">
                                                        <?php echo $province->name; ?></option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <?php echo form_error('province', '<small class="text-danger">', '</small>'); ?>
                                            </div>

                                            <div class="mb-3">
                                                <label for="city" class="form-label">Kabupaten/Kota <span
                                                        class="text-danger">*</span></label>
                                                <select class="form-select required" id="city" name="city">
                                                    <option value="" selected>Pilih Kabupaten/Kota</option>
                                                    <!-- Opsi kabupaten/kota akan diisi dengan JavaScript -->
                                                </select>
                                                <?php echo form_error('city', '<small class="text-danger">', '</small>'); ?>
                                            </div>

                                            <div class="mb-3">
                                                <label for="district" class="form-label">Kecamatan <span
                                                        class="text-danger">*</span></label>
                                                <select class="form-select required" id="district" name="district">
                                                    <option value="" selected>Pilih Kecamatan</option>
                                                    <!-- Opsi kecamatan akan diisi dengan JavaScript -->
                                                </select>
                                                <?php echo form_error('district', '<small class="text-danger">', '</small>'); ?>
                                            </div>

                                            <div class="mb-3">
                                                <label for="village" class="form-label">Kelurahan/Desa <span
                                                        class="text-danger">*</span></label>
                                                <select class="form-select required" id="village" name="village">
                                                    <option value="" selected>Pilih Kelurahan/Desa</option>
                                                    <!-- Opsi kelurahan/desa akan diisi dengan JavaScript -->
                                                </select>
                                                <?php echo form_error('village', '<small class="text-danger">', '</small>'); ?>
                                            </div>

                                            <div class="mb-0">
                                                <label for="address" class="form-label">Alamat Lengkap TPA <span
                                                        class="text-danger">*</span></label>
                                                <textarea class="form-control required" id="address" name="address"
                                                    rows="3"
                                                    placeholder="Masukkan alamat lengkap TPA"><?php echo set_value('address'); ?></textarea>
                                                <?php echo form_error('address', '<small class="text-danger">', '</small>'); ?>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Informasi Pemilik/Pengelola -->
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Informasi Pemilik/Pengelola TPA <span
                                                class="text-danger">*</span></label>
                                        <div class="card p-3 border">
                                            <div class="mb-3">
                                                <label for="fullName" class="form-label">Nama Pemilik/Pengelola
                                                    <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control required" id="fullName"
                                                    name="fullName" value="<?php echo set_value('fullName'); ?>">
                                                <small class="text-muted">Nama ini akan digunakan sebagai nama
                                                    akun</small>
                                                <?php echo form_error('fullName', '<small class="text-danger">', '</small>'); ?>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Jenis Kelamin Pemilik <span
                                                        class="text-danger">*</span></label>
                                                <div class="d-flex gap-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input required" type="radio"
                                                            name="gender" id="genderMale" value="laki-laki"
                                                            <?php echo set_radio('gender', 'laki-laki'); ?>>
                                                        <label class="form-check-label" for="genderMale">
                                                            Laki-laki
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input required" type="radio"
                                                            name="gender" id="genderFemale" value="perempuan"
                                                            <?php echo set_radio('gender', 'perempuan'); ?>>
                                                        <label class="form-check-label" for="genderFemale">
                                                            Perempuan
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input required" type="radio"
                                                            name="gender" id="genderOther" value="lainnya"
                                                            <?php echo set_radio('gender', 'lainnya'); ?>>
                                                        <label class="form-check-label" for="genderOther">
                                                            Lainnya
                                                        </label>
                                                    </div>
                                                </div>
                                                <?php echo form_error('gender', '<small class="text-danger">', '</small>'); ?>
                                            </div>

                                            <div class="mb-3">
                                                <label for="ownerPhoto" class="form-label">Foto Profil Pemilik <span
                                                        class="text-danger">*</span></label>
                                                <input type="file" class="form-control required" id="ownerPhoto"
                                                    name="ownerPhoto" accept="image/*">
                                                <small class="text-muted">Format: JPG, PNG, GIF. Maks: 2MB</small>
                                                <?php echo isset($error_ownerPhoto) ? '<small class="text-danger">' . $error_ownerPhoto . '</small>' : ''; ?>
                                            </div>

                                            <div class="mb-3">
                                                <label for="ownerIdentityCard" class="form-label">KTP Pemilik <span
                                                        class="text-danger">*</span></label>
                                                <input type="file" class="form-control required" id="ownerIdentityCard"
                                                    name="ownerIdentityCard" accept="image/*">
                                                <small class="text-muted">Format: JPG, PNG. Maks: 2MB</small>
                                                <?php echo isset($error_ownerIdentityCard) ? '<small class="text-danger">' . $error_ownerIdentityCard . '</small>' : ''; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Legalitas TPA -->
                                    <div class="mb-4">
                                        <label for="tpaLicense" class="form-label">Ijin Pendirian TPA/TPQ <span
                                                class="text-danger">*</span></label>
                                        <input type="file" class="form-control required" id="tpaLicense"
                                            name="tpaLicense" accept="image/*,application/pdf">
                                        <small class="text-muted">Format: JPG, PNG, PDF. Maks: 5MB</small>
                                        <?php echo isset($error_tpaLicense) ? '<small class="text-danger">' . $error_tpaLicense . '</small>' : ''; ?>
                                    </div>
                                    <div class="mb-3">
                                        <label for="password" class="form-label">Password <span
                                                class="text-danger">*</span></label>
                                        <input type="password" class="form-control required" id="password"
                                            name="password" minlength="6">
                                        <small class="text-muted">Minimal 6 karakter</small>
                                        <?php echo form_error('password', '<small class="text-danger">', '</small>'); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="confirmPassword" class="form-label">Konfirmasi Password <span
                                                class="text-danger">*</span></label>
                                        <input type="password" class="form-control required" id="confirmPassword"
                                            name="confirmPassword" minlength="6">
                                        <?php echo form_error('confirmPassword', '<small class="text-danger">', '</small>'); ?>
                                    </div>
                                </div>
                            </section>

                            <!-- Step 2: Program & Kurikulum -->
                            <h6>Program & Kurikulum</h6>
                            <section>
                                <div class="card card-body">
                                    <div class="mb-4">
                                        <label class="form-label">Program & Kurikulum <span
                                                class="text-danger">*</span></label>
                                        <div class="card p-3 border">
                                            <div class="table-responsive">
                                                <table class="table table-bordered" id="programTable">
                                                    <thead>
                                                        <tr>
                                                            <th>Nama Program</th>
                                                            <th>Deskripsi</th>
                                                            <th>Usia</th>
                                                            <th>Aksi</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td colspan="4" class="text-center">Belum ada data
                                                                program</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>

                                            <input type="hidden" id="programData" name="programData" value="[]">
                                            <div id="programValidationMessage" class="mt-2"></div>

                                            <div class="d-flex justify-content-end mt-2">
                                                <button type="button" class="btn btn-primary" id="addProgram">
                                                    <i class="fas fa-plus me-1"></i> Tambah Program
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- Step 3: Struktur Organisasi -->
                            <h6>Struktur Organisasi</h6>
                            <section>
                                <div class="card card-body">
                                    <div class="mb-4">
                                        <label class="form-label">Struktur Organisasi <span
                                                class="text-danger">*</span></label>
                                        <div class="card p-3 border">
                                            <div class="table-responsive">
                                                <table class="table table-bordered" id="organizationTable">
                                                    <thead>
                                                        <tr>
                                                            <th>Nama</th>
                                                            <th>Jabatan</th>
                                                            <th>Foto</th>
                                                            <th>Aksi</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td colspan="4" class="text-center">Belum ada data
                                                                struktur organisasi</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>

                                            <input type="hidden" id="organizationData" name="organizationData"
                                                value="[]">
                                            <div id="organizationValidationMessage" class="mt-2"></div>

                                            <div class="d-flex justify-content-end mt-2">
                                                <button type="button" class="btn btn-primary" id="addOrganization">
                                                    <i class="fas fa-plus me-1"></i> Tambah Anggota
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- Step 4: Galeri Foto -->
                            <h6>Galeri Foto</h6>
                            <section>
                                <div class="card card-body">
                                    <div class="mb-4">
                                        <label class="form-label">Galeri Foto TPA <span
                                                class="text-danger">*</span></label>
                                        <div class="card p-3 border">
                                            <div class="row" id="galleryPreview">
                                                <div class="col-12 text-center">
                                                    <p class="text-muted">Belum ada foto yang diunggah</p>
                                                </div>
                                            </div>

                                            <input type="hidden" id="galleryData" name="galleryData" value="[]">
                                            <div id="galleryValidationMessage" class="mt-2"></div>

                                            <div class="mt-3">
                                                <label for="galleryUpload" class="form-label">Unggah Foto <span
                                                        class="text-danger">*</span></label>
                                                <input type="file" class="form-control" id="galleryUpload"
                                                    accept="image/*" multiple>
                                                <small class="text-muted">Format: JPG, PNG. Maks: 2MB per foto.
                                                    Minimal 3 foto.</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>


                            <?php echo form_close(); ?>

                            <div class="d-flex align-items-center mt-4">
                                <p class="fs-4 mb-0 text-dark">Sudah memiliki Akun?</p>
                                <a class="text-primary fw-medium ms-2"
                                    href="<?php echo site_url('auth/login'); ?>">Masuk</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--  Import Js Files -->
<script src="<?php echo base_url('assets/libs/jquery/dist/jquery.min.js'); ?>"></script>
<script src="<?php echo base_url('assets/libs/simplebar/dist/simplebar.min.js'); ?>"></script>
<script src="<?php echo base_url('assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js'); ?>"></script>
<!-- core files -->
<script src="<?php echo base_url('assets/js/app.min.js') ?>"></script>
<script src="<?php echo base_url('assets/js/app.init.js') ?>"></script>
<script src="<?php echo base_url('assets/js/app-style-switcher.js') ?>"></script>
<script src="<?php echo base_url('assets/js/sidebarmenu.js') ?>"></script>

<script src="<?php echo base_url('assets/js/custom.js') ?>"></script>

<!-- Form wizard -->
<script src="<?php echo base_url('assets/libs/jquery-steps/build/jquery.steps.min.js'); ?>"></script>
<script src="<?php echo base_url('assets/libs/jquery-validation/dist/jquery.validate.min.js'); ?>"></script>

<script>
// Tambahkan custom validation method untuk program data
$.validator.addMethod("validProgramData", function(value, element) {
    if (!value || value === '[]') {
        return false;
    }

    try {
        const programArray = JSON.parse(value);
        return Array.isArray(programArray) && programArray.length > 0;
    } catch (e) {
        return false;
    }
}, "Minimal satu program harus ditambahkan");

// Tambahkan custom validation method untuk organization data
$.validator.addMethod("validOrganizationData", function(value, element) {
    if (!value || value === '[]') {
        return false;
    }

    try {
        const organizationArray = JSON.parse(value);
        return Array.isArray(organizationArray) && organizationArray.length > 0;
    } catch (e) {
        return false;
    }
}, "Minimal satu anggota organisasi harus ditambahkan");

// Tambahkan custom validation method untuk gallery data
$.validator.addMethod("validGalleryData", function(value, element) {
    if (!value || value === '[]') {
        return false;
    }

    try {
        const galleryArray = JSON.parse(value);
        return Array.isArray(galleryArray) && galleryArray.length >= 3;
    } catch (e) {
        return false;
    }
}, "Minimal 3 foto harus diunggah untuk galeri TPA");

// Script untuk mengisi dropdown provinsi, kabupaten/kota, dan kecamatan
$(document).ready(function() {
    // Ambil nilai yang sudah dipilih sebelumnya (dari set_value pada server)
    const selectedProvinceId = '<?php echo set_value('province'); ?>';
    const selectedCityId = '<?php echo set_value('city'); ?>';
    const selectedDistrictId = '<?php echo set_value('district'); ?>';
    const selectedVillageId = '<?php echo set_value('village'); ?>';
    document.getElementById('whatsapp').addEventListener('input', function(e) {
        this.value = this.value.replace(/[^0-9]/g, '');
    });

    // PASANG EVENT LISTENER
    $('#province').off('change').on('change', handleProvinceChange);
    $('#city').off('change').on('change', handleCityChange);
    $('#district').off('change').on('change', handleDistrictChange);

    // Jika ada provinsi yang sudah dipilih (misal error validation), langsung load kabupaten/kota
    if (selectedProvinceId) {
        loadCities(selectedProvinceId);
    }

    // Handler saat Provinsi di‐change
    function handleProvinceChange() {
        const provinceId = $('#province').val();

        // Reset dropdown kota & kecamatan
        $('#city').html('<option value="" selected>Pilih Kabupaten/Kota</option>');
        $('#district').html('<option value="" selected>Pilih Kecamatan</option>');

        if (provinceId) {
            loadCities(provinceId);
        }
    }

    // Handler saat Kabupaten/Kota di‐change
    function handleCityChange() {
        const cityId = $('#city').val();

        // Reset dropdown kecamatan
        $('#district').html('<option value="" selected>Pilih Kecamatan</option>');

        if (cityId) {
            loadDistricts(cityId);
        }
    }

    // Handler saat Kecamatan di‐change
    function handleDistrictChange() {
        const districtId = $('#district').val();

        // Reset dropdown desa
        $('#village').html('<option value="" selected>Pilih Kelurahan/Desa</option>');

        if (districtId) {
            loadVillages(districtId);
        }
    }

    // Fungsi untuk memuat data Kabupaten/Kota berdasarkan Provinsi
    function loadCities(provinceId) {
        const $citySelect = $('#city');
        if ($citySelect.length === 0) return;

        // Tampilkan status loading
        $citySelect.html('<option value="">Loading...</option>');

        $.ajax({
            url: '<?php echo site_url('api/location/cities'); ?>',
            type: 'POST',
            data: {
                provinceid: provinceId
            },
            dataType: 'json',
            success: function(response) {
                // Setelah berhasil, reset dropdown kota
                $citySelect.html('<option value="" selected>Pilih Kabupaten/Kota</option>');

                if (response.status === 'success' && Array.isArray(response.data)) {
                    response.data.forEach(function(city) {
                        const $opt = $('<option>')
                            .val(city.id)
                            .text(city.name);

                        // Jika ada nilai lama dari set_value, pilih kota tersebut
                        if (city.id == selectedCityId) {
                            $opt.prop('selected', true);
                        }

                        $citySelect.append($opt);
                    });

                    // Jika ada nilai lama kabupaten/kota, langsung load kecamatan
                    if (selectedCityId) {
                        loadDistricts(selectedCityId);
                    }
                } else {
                    // Jika respon JSON sukses tapi data tidak array, tetap tampilkan placeholder
                    $citySelect.html('<option value="" selected>Pilih Kabupaten/Kota</option>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading cities:', error);
                $citySelect.html('<option value="" selected>Error loading data</option>');
            }
        });
    }

    // Fungsi untuk memuat data Kecamatan berdasarkan Kabupaten/Kota
    function loadDistricts(cityId) {
        const $districtSelect = $('#district');
        if ($districtSelect.length === 0) return;

        // Tampilkan status loading
        $districtSelect.html('<option value="">Loading...</option>');

        $.ajax({
            url: '<?php echo site_url('api/location/districts'); ?>',
            type: 'POST',
            data: {
                cityid: cityId
            },
            dataType: 'json',
            success: function(response) {
                // Setelah berhasil, reset dropdown kecamatan
                $districtSelect.html('<option value="" selected>Pilih Kecamatan</option>');

                if (response.status === 'success' && Array.isArray(response.data)) {
                    response.data.forEach(function(district) {
                        const $opt = $('<option>')
                            .val(district.id)
                            .text(district.name);

                        // Jika ada nilai lama dari set_value, pilih kecamatan tersebut
                        if (district.id == selectedDistrictId) {
                            $opt.prop('selected', true);
                        }

                        $districtSelect.append($opt);
                    });
                } else {
                    // Jika respon JSON sukses tapi data tidak array, tetap tampilkan placeholder
                    $districtSelect.html('<option value="" selected>Pilih Kecamatan</option>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading districts:', error);
                $districtSelect.html('<option value="" selected>Error loading data</option>');
            }
        });
    }

    // Fungsi untuk memuat data Kelurahan/Desa berdasarkan Kecamatan
    function loadVillages(districtId) {
        const $villageSelect = $('#village');
        if ($villageSelect.length === 0) return;

        // Tampilkan status loading
        $villageSelect.html('<option value="">Loading...</option>');

        $.ajax({
            url: '<?php echo site_url('api/location/villages'); ?>',
            type: 'POST',
            data: {
                districtid: districtId
            },
            dataType: 'json',
            success: function(response) {
                // Setelah berhasil, reset dropdown desa
                $villageSelect.html('<option value="" selected>Pilih Kelurahan/Desa</option>');

                if (response.status === 'success' && Array.isArray(response.data)) {
                    response.data.forEach(function(village) {
                        const $opt = $('<option>')
                            .val(village.id)
                            .text(village.name);

                        // Jika ada nilai lama dari set_value, pilih desa tersebut
                        if (village.id == selectedVillageId) {
                            $opt.prop('selected', true);
                        }

                        $villageSelect.append($opt);
                    });
                } else {
                    // Jika respon JSON sukses tapi data tidak array, tetap tampilkan placeholder
                    $villageSelect.html(
                        '<option value="" selected>Pilih Kelurahan/Desa</option>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading villages:', error);
                $villageSelect.html('<option value="" selected>Error loading data</option>');
            }
        });
    }
});

// ===== SCRIPT UNTUK MENGELOLA TABEL PROGRAM & KURIKULUM =====
$(document).ready(function() {
    const $addProgramBtn = $('#addProgram');
    const $programTable = $('#programTable');
    const $programData = $('#programData');
    let programs = [];

    // Debug: Periksa apakah elemen ditemukan
    console.log('addProgramBtn:', $addProgramBtn.length);
    console.log('programTable:', $programTable.length);
    console.log('programData:', $programData.length);

    try {
        programs = JSON.parse($programData.val() || '[]');
    } catch (e) {
        console.error('Error parsing program data:', e);
        programs = [];
    }

    // Fungsi untuk menampilkan data program dalam tabel
    function renderProgramTable() {
        const $tbody = $programTable.find('tbody');
        $tbody.empty();

        if (programs.length === 0) {
            $tbody.html('<tr><td colspan="4" class="text-center">Belum ada data program</td></tr>');
        } else {
            programs.forEach((program, index) => {
                const $tr = $('<tr></tr>');
                $tr.html(`
                        <td>${program.name}</td>
                        <td>${program.description}</td>
                        <td>${program.ageRange}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger delete-program" data-index="${index}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    `);
                $tbody.append($tr);
            });

            // Tambahkan event listener untuk tombol hapus
            $('.delete-program').off('click').on('click', function() {
                const index = $(this).data('index');
                programs.splice(index, 1);
                updateProgramData();
                renderProgramTable();
            });
        }
    }

    // Fungsi untuk memperbarui input hidden dengan data program
    function updateProgramData() {
        $programData.val(JSON.stringify(programs));
        // Trigger validasi ulang untuk field programData
        $programData.valid();
        // Update validasi message
        updateProgramValidationStatus();
    }

    // Fungsi untuk update status validasi program
    function updateProgramValidationStatus() {
        const $message = $("#programValidationMessage");

        if (programs.length === 0) {
            $message.html(
                '<small class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Belum ada program yang ditambahkan. Minimal 1 program diperlukan untuk melanjutkan.</small>'
            );
        } else {
            $message.html('<small class="text-success"><i class="fas fa-check-circle me-1"></i>' + programs
                .length +
                ' program telah ditambahkan</small>');
        }
    }

    // Tampilkan data program yang sudah ada
    renderProgramTable();

    // Update status validasi awal
    updateProgramValidationStatus();

    // Buat modal untuk form tambah program
    const modalHtml = `
            <div class="modal fade" id="programModal" tabindex="-1" aria-labelledby="programModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="programModalLabel">Tambah Program</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="programName" class="form-label">Nama Program</label>
                                <input type="text" class="form-control" id="programName" placeholder="Contoh: Program Tahfidz" required>
                            </div>
                            <div class="mb-3">
                                <label for="programDescription" class="form-label">Deskripsi Program</label>
                                <textarea class="form-control" id="programDescription" rows="3" placeholder="Deskripsi singkat tentang program" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="programAge" class="form-label">Rentang Usia</label>
                                <input type="text" class="form-control" id="programAge" placeholder="Contoh: 5-12 tahun" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="button" class="btn btn-primary" id="saveProgram">Simpan</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

    // Tambahkan modal ke body jika belum ada
    if ($('#programModal').length === 0) {
        $('body').append(modalHtml);
    }

    // Event listener untuk tombol tambah program
    $addProgramBtn.off('click').on('click', function() {
        console.log('Add program button clicked');

        // Reset form modal
        $('#programName').val('');
        $('#programDescription').val('');
        $('#programAge').val('');

        // Tampilkan modal
        $('#programModal').modal('show');
    });

    // Event listener untuk tombol simpan di modal
    $(document).on('click', '#saveProgram', function() {
        console.log('Save program button clicked');

        const name = $('#programName').val();
        const description = $('#programDescription').val();
        const ageRange = $('#programAge').val();

        // Validasi input
        if (!name || !description || !ageRange) {
            alert('Semua field harus diisi!');
            return;
        }

        // Tambahkan data program baru
        programs.push({
            name,
            description,
            ageRange
        });

        // Perbarui data dan tampilan
        updateProgramData();
        renderProgramTable();

        // Tutup modal
        $('#programModal').modal('hide');
    });
});

// Script untuk mengelola tabel struktur organisasi
$(document).ready(function() {
    const $addOrganizationBtn = $('#addOrganization');
    const $organizationTable = $('#organizationTable');
    const $organizationData = $('#organizationData');
    let organizations = [];

    // Debug: Periksa apakah elemen ditemukan
    console.log('addOrganizationBtn:', $addOrganizationBtn.length);
    console.log('organizationTable:', $organizationTable.length);
    console.log('organizationData:', $organizationData.length);

    try {
        organizations = JSON.parse($organizationData.val() || '[]');
    } catch (e) {
        console.error('Error parsing organization data:', e);
        organizations = [];
    }

    // Fungsi untuk menampilkan data struktur organisasi dalam tabel
    function renderOrganizationTable() {
        const $tbody = $organizationTable.find('tbody');
        $tbody.empty();

        if (organizations.length === 0) {
            $tbody.html(
                '<tr><td colspan="4" class="text-center">Belum ada data struktur organisasi</td></tr>');
        } else {
            organizations.forEach((org, index) => {
                const $tr = $('<tr></tr>');
                $tr.html(`
                        <td>${org.name}</td>
                        <td>${org.position}</td>
                        <td>${org.photo ? '<span class="badge bg-success">Uploaded</span>' : '<span class="badge bg-danger">Not Uploaded</span>'}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger delete-org" data-index="${index}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    `);
                $tbody.append($tr);
            });

            // Tambahkan event listener untuk tombol hapus
            $('.delete-org').off('click').on('click', function() {
                const index = $(this).data('index');
                organizations.splice(index, 1);
                updateOrganizationData();
                renderOrganizationTable();
            });
        }
    }

    // Fungsi untuk memperbarui input hidden dengan data struktur organisasi
    function updateOrganizationData() {
        $organizationData.val(JSON.stringify(organizations));
        // Trigger validasi ulang untuk field organizationData
        $organizationData.valid();
        // Update validasi message
        updateOrganizationValidationStatus();
    }

    // Fungsi untuk update status validasi struktur organisasi
    function updateOrganizationValidationStatus() {
        const $message = $("#organizationValidationMessage");

        if (organizations.length === 0) {
            $message.html(
                '<small class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Belum ada anggota organisasi yang ditambahkan. Minimal 1 anggota diperlukan untuk melanjutkan.</small>'
            );
        } else {
            $message.html('<small class="text-success"><i class="fas fa-check-circle me-1"></i>' +
                organizations.length +
                ' anggota organisasi telah ditambahkan</small>');
        }
    }

    // Tampilkan data struktur organisasi yang sudah ada
    renderOrganizationTable();

    // Update status validasi awal
    updateOrganizationValidationStatus();

    // Buat modal untuk form tambah anggota
    const modalHtml = `
            <div class="modal fade" id="organizationModal" tabindex="-1" aria-labelledby="organizationModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="organizationModalLabel">Tambah Anggota Organisasi</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="orgName" class="form-label">Nama Anggota</label>
                                <input type="text" class="form-control" id="orgName" required>
                            </div>
                            <div class="mb-3">
                                <label for="orgPosition" class="form-label">Jabatan</label>
                                <input type="text" class="form-control" id="orgPosition" placeholder="Contoh: Ketua, Sekretaris, Bendahara" required>
                            </div>
                            <div class="mb-3">
                                <label for="orgPhoto" class="form-label">Foto</label>
                                <input type="file" class="form-control" id="orgPhoto" name="orgPhoto" accept="image/*">
                                <small class="text-muted">Format: JPG, PNG. Maks: 2MB</small>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="button" class="btn btn-primary" id="saveOrganization">Simpan</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

    // Tambahkan modal ke body jika belum ada
    if ($('#organizationModal').length === 0) {
        $('body').append(modalHtml);
    }

    // Event listener untuk tombol tambah anggota
    $addOrganizationBtn.off('click').on('click', function() {
        console.log('Add organization button clicked');

        // Reset form modal
        $('#orgName').val('');
        $('#orgPosition').val('');
        $('#orgPhoto').val('');

        // Tampilkan modal
        $('#organizationModal').modal('show');
    });

    // Event listener untuk tombol simpan di modal
    $(document).on('click', '#saveOrganization', function() {
        console.log('Save organization button clicked');

        const name = $('#orgName').val();
        const position = $('#orgPosition').val();
        const photoInput = document.getElementById('orgPhoto');
        const hasPhoto = photoInput.files.length > 0;

        // Validasi input
        if (!name || !position) {
            alert('Nama dan Jabatan harus diisi!');
            return;
        }

        // Tambahkan data anggota baru
        const newIndex = organizations.length;
        organizations.push({
            name,
            position,
            photo: hasPhoto
        });

        // Jika ada foto yang diupload, tambahkan ke form
        if (hasPhoto) {
            const file = photoInput.files[0];

            // Buat input file baru untuk menyimpan foto
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'file';
            hiddenInput.name = 'orgPhoto_' + newIndex;
            hiddenInput.style.display = 'none';
            hiddenInput.className = 'org-photo-input';

            // Buat DataTransfer object untuk menyimpan file
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            hiddenInput.files = dataTransfer.files;

            // Tambahkan ke form
            $('form.validation-wizard').append(hiddenInput);
        }

        // Perbarui data dan tampilan
        updateOrganizationData();
        renderOrganizationTable();

        // Tutup modal
        $('#organizationModal').modal('hide');
    });
});

// Script untuk mengelola galeri foto
$(document).ready(function() {
    const $galleryUpload = $('#galleryUpload');
    const $galleryPreview = $('#galleryPreview');
    const $galleryData = $('#galleryData');
    let gallery = [];

    // Debug: Periksa apakah elemen ditemukan
    console.log('galleryUpload:', $galleryUpload.length);
    console.log('galleryPreview:', $galleryPreview.length);
    console.log('galleryData:', $galleryData.length);

    try {
        gallery = JSON.parse($galleryData.val() || '[]');
    } catch (e) {
        console.error('Error parsing gallery data:', e);
        gallery = [];
    }

    // Fungsi untuk menampilkan preview galeri
    function renderGalleryPreview() {
        $galleryPreview.empty();

        if (gallery.length === 0) {
            $galleryPreview.html(`
                    <div class="col-12 text-center">
                        <p class="text-muted">Belum ada foto yang diunggah</p>
                    </div>
                `);
        } else {
            gallery.forEach((item, index) => {
                const $col = $('<div class="col-md-4 mb-3"></div>');
                $col.html(`
                        <div class="card">
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                ${item.previewUrl ? 
                                    `<img src="${item.previewUrl}" class="img-fluid" style="max-height: 100%; object-fit: cover;">` : 
                                    `<span class="text-success"><i class="fas fa-image fa-3x"></i></span>`
                                }
                            </div>
                            <div class="card-body">
                                <h5 class="card-title">Foto ${index + 1}</h5>
                                <p class="card-text small text-muted">${item.name}</p>
                                <button type="button" class="btn btn-sm btn-danger delete-photo" data-index="${index}">
                                    <i class="fas fa-trash"></i> Hapus
                                </button>
                            </div>
                        </div>
                    `);
                $galleryPreview.append($col);
            });

            // Tambahkan event listener untuk tombol hapus
            $('.delete-photo').off('click').on('click', function() {
                const index = $(this).data('index');

                // Hapus input file tersembunyi jika ada
                $(`input[name="galleryPhoto_${index}"]`).remove();

                gallery.splice(index, 1);
                updateGalleryData();
                renderGalleryPreview();

                // Update counter
                updatePhotoCounter();
            });
        }
    }

    // Fungsi untuk memperbarui input hidden dengan data galeri
    function updateGalleryData() {
        $galleryData.val(JSON.stringify(gallery));
        // Trigger validasi ulang untuk field galleryData
        $galleryData.valid();
        // Update validasi message
        updateGalleryValidationStatus();
    }

    // Fungsi untuk update status validasi galeri
    function updateGalleryValidationStatus() {
        const $message = $("#galleryValidationMessage");

        if (gallery.length < 3) {
            $message.html(
                '<small class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Belum ada foto yang diunggah atau kurang dari 3 foto. Minimal 3 foto diperlukan untuk mendaftar.</small>'
            );
        } else {
            $message.html('<small class="text-success"><i class="fas fa-check-circle me-1"></i>' + gallery
                .length +
                ' foto telah diunggah</small>');
        }
    }

    // Fungsi untuk memperbarui counter foto
    function updatePhotoCounter() {
        const count = gallery.length;
        const $counter = $('#galleryCounter');

        if ($counter.length === 0) {
            // Buat counter jika belum ada
            const $counterHtml = $(`
                    <div class="alert ${count >= 3 ? 'alert-success' : 'alert-warning'} mt-3" id="galleryCounter">
                        <i class="${count >= 3 ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'} me-2"></i>
                        Jumlah foto: <strong>${count}</strong> ${count >= 3 ? '(Sudah memenuhi syarat)' : '(Minimal 3 foto)'}
                    </div>
                `);
            $galleryUpload.closest('.mt-3').after($counterHtml);
        } else {
            // Update counter yang sudah ada
            $counter.removeClass('alert-success alert-warning')
                .addClass(count >= 3 ? 'alert-success' : 'alert-warning')
                .html(`
                            <i class="${count >= 3 ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'} me-2"></i>
                            Jumlah foto: <strong>${count}</strong> ${count >= 3 ? '(Sudah memenuhi syarat)' : '(Minimal 3 foto)'}
                       `);
        }
    }

    // Tampilkan preview galeri yang sudah ada
    renderGalleryPreview();

    // Update counter awal
    updatePhotoCounter();

    // Update status validasi awal
    updateGalleryValidationStatus();

    // Event listener untuk upload foto galeri
    $galleryUpload.on('change', function() {
        const files = this.files;

        if (files.length > 0) {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];

                // Validasi ukuran file (max 2MB)
                if (file.size > 2 * 1024 * 1024) {
                    alert(`File "${file.name}" terlalu besar. Maksimal 2 MB.`);
                    continue;
                }

                // Validasi tipe file
                if (!file.type.match('image.*')) {
                    alert(`File "${file.name}" bukan gambar.`);
                    continue;
                }

                // Buat preview URL
                const previewUrl = URL.createObjectURL(file);

                // Tambahkan ke galeri
                const newIndex = gallery.length;
                gallery.push({
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    previewUrl: previewUrl
                });

                // Buat input file tersembunyi untuk menyimpan file
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'file';
                hiddenInput.name = 'galleryPhoto_' + newIndex;
                hiddenInput.style.display = 'none';
                hiddenInput.className = 'gallery-photo-input';

                // Buat DataTransfer object untuk menyimpan file
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                hiddenInput.files = dataTransfer.files;

                // Tambahkan ke form
                $('form.validation-wizard').append(hiddenInput);
            }

            // Perbarui data dan tampilan
            updateGalleryData();
            renderGalleryPreview();
            updatePhotoCounter();

            // Reset input file
            this.value = '';
        }
    });

    // Tambahkan validasi khusus untuk galeri foto
    $.validator.addMethod("minGalleryPhotos", function(value, element) {
        return gallery.length >= 3;
    }, "Minimal 3 foto harus diunggah untuk galeri TPA.");

    // Tambahkan aturan validasi ke form
    $('form.validation-wizard').validate({
        rules: {
            galleryData: {
                minGalleryPhotos: true
            }
        },
        messages: {
            galleryData: {
                minGalleryPhotos: "Minimal 3 foto harus diunggah untuk galeri TPA."
            }
        },
        errorPlacement: function(error, element) {
            if (element.attr("name") === "galleryData") {
                error.insertAfter("#galleryCounter");
            } else {
                element.after(error);
            }
        }
    });

    // Tambahkan tombol bantuan untuk galeri
    const $helpButton = $(`
            <button type="button" class="btn btn-sm btn-info ms-2" id="galleryHelpBtn">
                <i class="fas fa-question-circle"></i> Bantuan
            </button>
        `);

    $galleryUpload.after($helpButton);

    // Event listener untuk tombol bantuan
    $('#galleryHelpBtn').on('click', function() {
        const helpHtml = `
                <div class="modal fade" id="galleryHelpModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Bantuan Upload Foto Galeri</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>Untuk mengunggah foto galeri TPA, ikuti langkah-langkah berikut:</p>
                                <ol>
                                    <li>Klik tombol "Browse" atau "Choose File"</li>
                                    <li>Pilih foto yang ingin diunggah (bisa memilih beberapa sekaligus dengan menekan tombol Ctrl atau Cmd)</li>
                                    <li>Klik "Open" untuk mengunggah foto</li>
                                    <li>Foto yang berhasil diunggah akan muncul di preview</li>
                                    <li>Ulangi langkah di atas jika ingin menambah foto lagi</li>
                                </ol>
                                <p>Catatan penting:</p>
                                <ul>
                                    <li>Minimal harus mengunggah 3 foto</li>
                                    <li>Format foto yang diperbolehkan: JPG, PNG</li>
                                    <li>Ukuran maksimal per foto: 2MB</li>
                                </ul>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

        // Tambahkan modal ke body jika belum ada
        if ($('#galleryHelpModal').length === 0) {
            $('body').append(helpHtml);
        }

        // Tampilkan modal
        $('#galleryHelpModal').modal('show');
    });
});

// Modifikasi konfigurasi Form Wizard untuk menambahkan validasi galeri
$(".validation-wizard").steps({
    headerTag: "h6",
    bodyTag: "section",
    transitionEffect: "fade",
    titleTemplate: '<span class="step">#index#</span> #title#',
    labels: {
        finish: "Daftar",
        next: "Selanjutnya",
        previous: "Sebelumnya"
    },
    onStepChanging: function(event, currentIndex, newIndex) {
        // Validasi form saat pindah step
        var form = $(this);

        // Selalu izinkan untuk kembali ke step sebelumnya
        if (currentIndex > newIndex) {
            return true;
        }

        // Validasi khusus untuk step Program & Kurikulum (index 1)
        if (currentIndex === 1 && newIndex > currentIndex) {
            // Cek apakah program sudah ditambahkan
            const programData = $("#programData").val();
            let programArray = [];
            try {
                programArray = JSON.parse(programData || '[]');
            } catch (e) {
                programArray = [];
            }

            // Jika program belum ditambahkan, tampilkan pesan error dan tetap di step ini
            if (programArray.length === 0) {
                // Update pesan validasi untuk program
                $("#programValidationMessage").html(
                    '<small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Minimal satu program harus ditambahkan sebelum melanjutkan</small>'
                );

                // Scroll ke bagian yang error
                $('html, body').animate({
                    scrollTop: $("#programTable").offset().top - 100
                }, 500);

                return false; // Mencegah perpindahan ke step selanjutnya
            }
        }

        // Validasi khusus untuk step Struktur Organisasi (index 2)
        if (currentIndex === 2 && newIndex > currentIndex) {
            // Cek apakah struktur organisasi sudah ditambahkan
            const organizationData = $("#organizationData").val();
            let organizationArray = [];
            try {
                organizationArray = JSON.parse(organizationData || '[]');
            } catch (e) {
                organizationArray = [];
            }

            // Jika struktur organisasi belum ditambahkan, tampilkan pesan error dan tetap di step ini
            if (organizationArray.length === 0) {
                // Update pesan validasi untuk struktur organisasi
                $("#organizationValidationMessage").html(
                    '<small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Minimal satu anggota organisasi harus ditambahkan sebelum melanjutkan</small>'
                );

                // Scroll ke bagian yang error
                $('html, body').animate({
                    scrollTop: $("#organizationTable").offset().top - 100
                }, 500);

                return false; // Mencegah perpindahan ke step selanjutnya
            }
        }

        // Validasi form pada step saat ini
        if (currentIndex < newIndex) {
            form.find('.body:eq(' + newIndex + ') label.error').remove();
            form.find('.body:eq(' + newIndex + ') .error').removeClass('error');
        }

        form.validate().settings.ignore = ":disabled,:hidden";
        return form.valid();
    },
    onFinishing: function(event, currentIndex) {
        var form = $(this);
        form.validate().settings.ignore = ":disabled";

        // Validasi khusus untuk step Galeri Foto (index 3) sebelum submit
        if (currentIndex === 3) {
            // Cek apakah galeri foto sudah ditambahkan
            const galleryData = $("#galleryData").val();
            let galleryArray = [];
            try {
                galleryArray = JSON.parse(galleryData || '[]');
            } catch (e) {
                galleryArray = [];
            }

            // Jika galeri foto belum mencukupi (minimal 3), tampilkan pesan error dan tetap di step ini
            if (galleryArray.length < 3) {
                // Update pesan validasi untuk galeri
                $("#galleryValidationMessage").html(
                    '<small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Minimal 3 foto harus diunggah sebelum mendaftar</small>'
                );

                // Scroll ke bagian yang error
                $('html, body').animate({
                    scrollTop: $("#galleryUpload").offset().top - 100
                }, 500);

                return false; // Mencegah submit form
            }
        }

        return form.valid();
    },
    onFinished: function(event, currentIndex) {
        // Validasi jumlah foto galeri sekali lagi
        const galleryData = $("#galleryData").val();
        let galleryArray = [];
        try {
            galleryArray = JSON.parse(galleryData || '[]');
        } catch (e) {
            galleryArray = [];
        }

        if (galleryArray.length < 3) {
            // Update pesan validasi untuk galeri
            $("#galleryValidationMessage").html(
                '<small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Minimal 3 foto harus diunggah sebelum mendaftar</small>'
            );

            // Scroll ke bagian yang error
            $('html, body').animate({
                scrollTop: $("#galleryUpload").offset().top - 100
            }, 500);

            return false;
        }

        // Submit form saat selesai
        $(this).submit();
    },
    onStepChanged: function(event, currentIndex, priorIndex) {
        // Update status validasi saat step berubah
        if (currentIndex === 1) {
            // Update status validasi untuk program
            const programs = JSON.parse($("#programData").val() || '[]');
            const $message = $("#programValidationMessage");

            if (programs.length === 0) {
                $message.html(
                    '<small class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Belum ada program yang ditambahkan. Minimal 1 program diperlukan untuk melanjutkan.</small>'
                );
            } else {
                $message.html('<small class="text-success"><i class="fas fa-check-circle me-1"></i>' +
                    programs.length +
                    ' program telah ditambahkan</small>');
            }
        } else if (currentIndex === 2) {
            // Update status validasi untuk struktur organisasi
            const organizations = JSON.parse($("#organizationData").val() || '[]');
            const $message = $("#organizationValidationMessage");

            if (organizations.length === 0) {
                $message.html(
                    '<small class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Belum ada anggota organisasi yang ditambahkan. Minimal 1 anggota diperlukan untuk melanjutkan.</small>'
                );
            } else {
                $message.html('<small class="text-success"><i class="fas fa-check-circle me-1"></i>' +
                    organizations.length +
                    ' anggota organisasi telah ditambahkan</small>');
            }
        } else if (currentIndex === 3) {
            // Update status validasi untuk galeri foto
            const gallery = JSON.parse($("#galleryData").val() || '[]');
            const $message = $("#galleryValidationMessage");

            if (gallery.length < 3) {
                $message.html(
                    '<small class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Belum ada foto yang diunggah atau kurang dari 3 foto. Minimal 3 foto diperlukan untuk mendaftar.</small>'
                );
            } else {
                $message.html('<small class="text-success"><i class="fas fa-check-circle me-1"></i>' +
                    gallery.length +
                    ' foto telah diunggah</small>');
            }
        }
    }
}).validate({
    errorPlacement: function(error, element) {
        element.after(error);
    },
    rules: {
        programData: {
            validProgramData: true
        },
        organizationData: {
            validOrganizationData: true
        },
        galleryData: {
            validGalleryData: true
        }
    },
    messages: {
        programData: {
            validProgramData: "Minimal satu program harus ditambahkan"
        },
        organizationData: {
            validOrganizationData: "Minimal satu anggota organisasi harus ditambahkan"
        },
        galleryData: {
            validGalleryData: "Minimal 3 foto harus diunggah untuk galeri TPA"
        }
    }
});
</script>
</body>

</html>