<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <!--  Title -->
    <title>Daftar sebagai <?= ucfirst($type) ?> - <PERSON><PERSON><PERSON> Go</title>
    <!--  Required Meta Tag -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="handheldfriendly" content="true" />
    <meta name="MobileOptimized" content="width" />
    <meta name="description" content="Daftar Ngaji Go" />
    <meta name="author" content="" />
    <meta name="keywords" content="Daftar" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--  Favicon -->
    <link rel="shortcut icon" type="image/png" href="<?php echo base_url('assets/images/logos/favicon.png'); ?>" />
    <!-- Core Css -->
    <link id="themeColors" rel="stylesheet" href="<?php echo base_url('assets/css/style.min.css'); ?>" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
</head>

<body>
    <!-- Preloader -->
    <div class="preloader">
        <img src="<?php echo base_url('assets/images/logos/favicon.png'); ?>" alt="loader"
            class="lds-ripple img-fluid" />
    </div>
    <!--  Body Wrapper -->
    <div class="page-wrapper" id="main-wrapper" data-layout="vertical" data-sidebartype="full"
        data-sidebar-position="fixed" data-header-position="fixed">
        <div
            class="position-relative overflow-hidden radial-gradient min-vh-100 d-flex align-items-center justify-content-center p-5">
            <div class="d-flex align-items-center justify-content-center w-100">
                <div class="row justify-content-center w-100">
                    <div class="col-md-12 col-lg-8 col-xxl-6">
                        <div class="card mb-0">
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-sm-6">
                                        <h2 class="mb-3 fs-7 fw-bolder"><?php echo $title; ?></h2>
                                    </div>

                                    <div class="col-sm-6 text-end">
                                        <div class="btn-group w-100" role="group">
                                            <a href="<?php echo site_url('auth/register/member'); ?>"
                                                class="btn <?php echo ($type == 'member') ? 'btn-primary' : 'btn-outline-primary'; ?>">Member</a>
                                            <a href="<?php echo site_url('auth/register/guru'); ?>"
                                                class="btn <?php echo ($type == 'guru') ? 'btn-primary' : 'btn-outline-primary'; ?>">Guru</a>
                                            <a href="<?php echo site_url('auth/register/tpa'); ?>"
                                                class="btn <?php echo ($type == 'tpa') ? 'btn-primary' : 'btn-outline-primary'; ?>">TPA</a>
                                        </div>
                                    </div>
                                </div>
                                <p class="mb-4">Silahkan lengkapi data berikut untuk mendaftar sebagai
                                    <?php echo $type; ?>. <small class="text-danger">* Wajib diisi</small></p>

                                <?php if (isset($error)): ?>
                                <?php echo $error; ?>
                                <?php endif; ?>

                                <?php if ($this->session->flashdata('message')): ?>
                                <?php echo $this->session->flashdata('message'); ?>
                                <?php endif; ?>

                                <?php echo form_open_multipart('auth/register/' . $type, ['class' => 'validation-wizard wizard-circle', 'id' => 'registerForm']); ?>

                                <!-- Form fields for all user types -->
                                <div class="mb-3">
                                    <label for="fullName" class="form-label">Nama Lengkap <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control required" id="fullName" name="fullName"
                                        value="<?php echo set_value('fullName'); ?>">

                                    <?php echo form_error('fullName', '<small class="text-danger">', '</small>'); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="phonenumber" class="form-label">Nomor WhatsApp Aktif <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="phonenumber" name="phonenumber"
                                        value="<?php echo set_value('phonenumber'); ?>">

                                    <?php echo form_error('phonenumber', '<small class="text-danger">', '</small>'); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">Email <span
                                            class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email"
                                        value="<?php echo set_value('email'); ?>">

                                    <?php echo form_error('email', '<small class="text-danger">', '</small>'); ?>
                                </div>

                                <!-- Jenis Kelamin -->
                                <div class="mb-3">
                                    <label class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
                                    <div class="d-flex gap-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="gender" id="genderMale"
                                                value="laki-laki" <?php echo set_radio('gender', 'laki-laki'); ?>>
                                            <label class="form-check-label" for="genderMale">
                                                <i class="ti ti-user me-1"></i>Laki-laki
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="gender" id="genderFemale"
                                                value="perempuan" <?php echo set_radio('gender', 'perempuan'); ?>>
                                            <label class="form-check-label" for="genderFemale">
                                                <i class="ti ti-user me-1"></i>Perempuan
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="gender" id="genderOther"
                                                value="lainnya" <?php echo set_radio('gender', 'lainnya'); ?>>
                                            <label class="form-check-label" for="genderOther">
                                                <i class="ti ti-user me-1"></i>Lainnya
                                            </label>
                                        </div>
                                    </div>

                                    <?php echo form_error('gender', '<small class="text-danger">', '</small>'); ?>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Alamat <span class="text-danger">*</span></label>
                                    <div class="card p-3 border">
                                        <div class="mb-3">
                                            <label for="province" class="form-label">Provinsi <span
                                                    class="text-danger">*</span></label>
                                            <select class="form-select" id="province" name="province"
                                                autocomplete="off">
                                                <option value="" selected>Pilih Provinsi</option>
                                                <?php foreach ($provinces as $province): ?>
                                                <option value="<?php echo $province->id; ?>"
                                                    <?php echo set_select('province', $province->id); ?>>
                                                    <?php echo $province->name; ?></option>
                                                <?php endforeach; ?>
                                            </select>

                                            <?php echo form_error('province', '<small class="text-danger">', '</small>'); ?>
                                        </div>

                                        <div class="mb-3">
                                            <label for="city" class="form-label">Kabupaten/Kota <span
                                                    class="text-danger">*</span></label>
                                            <select class="form-select" id="city" name="city" autocomplete="off">
                                                <option value="" selected>Pilih Kabupaten/Kota</option>
                                                <!-- Opsi kabupaten/kota akan diisi dengan JavaScript -->
                                            </select>

                                            <?php echo form_error('city', '<small class="text-danger">', '</small>'); ?>
                                        </div>

                                        <div class="mb-3">
                                            <label for="district" class="form-label">Kecamatan <span
                                                    class="text-danger">*</span></label>
                                            <select class="form-select" id="district" name="district"
                                                autocomplete="off">
                                                <option value="" selected>Pilih Kecamatan</option>
                                                <!-- Opsi kecamatan akan diisi dengan JavaScript -->
                                            </select>

                                            <?php echo form_error('district', '<small class="text-danger">', '</small>'); ?>
                                        </div>

                                        <div class="mb-3">
                                            <label for="village" class="form-label">Kelurahan/Desa <span
                                                    class="text-danger">*</span></label>
                                            <select class="form-select" id="village" name="village" autocomplete="off">
                                                <option value="" selected>Pilih Kelurahan/Desa</option>
                                                <!-- Opsi kelurahan/desa akan diisi dengan JavaScript -->
                                            </select>

                                            <?php echo form_error('village', '<small class="text-danger">', '</small>'); ?>
                                        </div>

                                        <div class="mb-0">
                                            <label for="address" class="form-label">Alamat Lengkap <span
                                                    class="text-danger">*</span></label>
                                            <textarea class="form-control" id="address" name="address" rows="3"
                                                placeholder="Masukkan alamat lengkap (nama jalan, nomor rumah, RT/RW, dll)"><?php echo set_value('address'); ?></textarea>

                                            <?php echo form_error('address', '<small class="text-danger">', '</small>'); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label">Password <span
                                            class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="password" name="password">

                                    <?php echo form_error('password', '<small class="text-danger">', '</small>'); ?>
                                </div>

                                <div class="mb-4">
                                    <label for="confirmPassword" class="form-label">Konfirmasi Password <span
                                            class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="confirmPassword"
                                        name="confirmPassword">

                                    <?php echo form_error('confirmPassword', '<small class="text-danger">', '</small>'); ?>
                                </div>

                                <button type="submit" class="btn btn-primary w-100 py-8 mb-4 rounded-2">Daftar</button>

                                <div class="d-flex align-items-center">
                                    <p class="fs-4 mb-0 text-dark">Sudah memiliki Akun?</p>
                                    <a class="text-primary fw-medium ms-2"
                                        href="<?php echo site_url('auth/login'); ?>">Masuk</a>
                                </div>
                                <?php echo form_close(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--  Import Js Files -->
    <script src="<?php echo base_url('assets/libs/jquery/dist/jquery.min.js'); ?>"></script>
    <script src="<?php echo base_url('assets/libs/simplebar/dist/simplebar.min.js'); ?>"></script>
    <script src="<?php echo base_url('assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js'); ?>"></script>
    <!-- core files -->
    <script src="<?php echo base_url('assets/js/app.min.js') ?>"></script>
    <script src="<?php echo base_url('assets/js/app.init.js') ?>"></script>
    <script src="<?php echo base_url('assets/js/app-style-switcher.js') ?>"></script>
    <script src="<?php echo base_url('assets/js/sidebarmenu.js') ?>"></script>

    <script src="<?php echo base_url('assets/js/custom.js') ?>"></script>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/additional-methods.min.js"></script>

    <script>
    $(function() {
        // Penempatan pesan error
    }).validate({
        errorPlacement: function(error, element) {
            element.after(error);
        },
        // Aturan validasi
        rules: {
            fullName: {
                required: true,
                minlength: 3
            },
            phonenumber: {
                required: true,
                digits: true,
                minlength: 10,
                maxlength: 15
            },
            email: {
                required: true,
                email: true
            },
            gender: {
                required: true
            },
            province: {
                required: true
            },
            city: {
                required: true
            },
            district: {
                required: true
            },
            village: {
                required: true
            },
            address: {
                required: true,
                minlength: 10
            },
            password: {
                required: true,
                minlength: 6
            },
            confirmPassword: {
                required: true,
                equalTo: "#password"
            }
        },
        // Pesan error kustom
        messages: {
            fullName: {
                required: "Nama lengkap wajib diisi",
                minlength: "Nama lengkap minimal 3 karakter"
            },
            phonenumber: {
                required: "Nomor WhatsApp wajib diisi",
                digits: "Nomor WhatsApp hanya boleh berisi angka",
                minlength: "Nomor WhatsApp minimal 10 digit",
                maxlength: "Nomor WhatsApp maksimal 15 digit"
            },
            email: {
                required: "Email wajib diisi",
                email: "Format email tidak valid"
            },
            gender: {
                required: "Jenis kelamin wajib dipilih"
            },
            province: {
                required: "Provinsi wajib dipilih"
            },
            city: {
                required: "Kabupaten/Kota wajib dipilih"
            },
            district: {
                required: "Kecamatan wajib dipilih"
            },
            village: {
                required: "Kelurahan/Desa wajib dipilih"
            },
            address: {
                required: "Alamat lengkap wajib diisi",
                minlength: "Alamat terlalu pendek, minimal 10 karakter"
            },
            password: {
                required: "Password wajib diisi",
                minlength: "Password minimal 6 karakter"
            },
            confirmPassword: {
                required: "Konfirmasi password wajib diisi",
                equalTo: "Konfirmasi password tidak sama dengan password"
            }
        },

    });
    // Event listener untuk perubahan provinsi
    document.getElementById('province').addEventListener('change', function() {
        // Ambil data kabupaten/kota berdasarkan provinsi yang dipilih
        const provinceId = this.value;

        // Reset dropdown kota, kecamatan, dan desa saat provinsi diubah
        const citySelect = document.getElementById('city');
        citySelect.innerHTML = '<option value="" selected>Pilih Kabupaten/Kota</option>';

        const districtSelect = document.getElementById('district');
        districtSelect.innerHTML = '<option value="" selected>Pilih Kecamatan</option>';

        const villageSelect = document.getElementById('village');
        villageSelect.innerHTML = '<option value="" selected>Pilih Kelurahan/Desa</option>';

        if (provinceId) {
            loadCities(provinceId);
            hideError(this);
        }
    });

    // Event listener untuk perubahan kabupaten/kota
    document.getElementById('city').addEventListener('change', function() {
        // Ambil data kecamatan berdasarkan kabupaten/kota yang dipilih
        const cityId = this.value;

        // Reset dropdown kecamatan dan desa saat kota diubah
        const districtSelect = document.getElementById('district');
        districtSelect.innerHTML = '<option value="" selected>Pilih Kecamatan</option>';

        const villageSelect = document.getElementById('village');
        villageSelect.innerHTML = '<option value="" selected>Pilih Kelurahan/Desa</option>';

        if (cityId) {
            loadDistricts(cityId);
            hideError(this);
        }
    });

    // Event listener untuk perubahan kecamatan
    document.getElementById('district').addEventListener('change', function() {
        // Ambil data desa berdasarkan kecamatan yang dipilih
        const districtId = this.value;

        // Reset dropdown desa saat kecamatan diubah
        const villageSelect = document.getElementById('village');
        villageSelect.innerHTML = '<option value="" selected>Pilih Kelurahan/Desa</option>';

        if (districtId) {
            loadVillages(districtId);
            hideError(this);
        }
    });

    // Event listener untuk perubahan desa
    document.getElementById('village').addEventListener('change', function() {
        if (this.value) {
            hideError(this);
        }
    });

    // Fungsi untuk memuat data kabupaten/kota
    function loadCities(provinceId) {
        // Tampilkan loading state
        const citySelect = document.getElementById('city');
        citySelect.innerHTML = '<option value="">Loading...</option>';

        // Buat form data untuk request
        const formData = new FormData();
        formData.append('provinceid', provinceId);

        // Kirim request POST ke API
        fetch('<?php echo site_url('api/location/cities'); ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Reset dropdown
                citySelect.innerHTML = '<option value="" selected>Pilih Kabupaten/Kota</option>';

                // Periksa apakah response berhasil
                if (data.status === 'success' && Array.isArray(data.data)) {
                    // Tambahkan opsi kota dari response
                    data.data.forEach(city => {
                        const option = document.createElement('option');
                        option.value = city.id;
                        option.textContent = city.name;
                        citySelect.appendChild(option);
                    });
                } else {
                    console.error('Invalid response format:', data);
                }
            })
            .catch(error => {
                console.error('Error loading cities:', error);
                citySelect.innerHTML = '<option value="" selected>Error loading data</option>';
            });
    }

    // Fungsi untuk memuat data kecamatan
    function loadDistricts(cityId) {
        // Tampilkan loading state
        const districtSelect = document.getElementById('district');
        districtSelect.innerHTML = '<option value="">Loading...</option>';

        // Buat form data untuk request
        const formData = new FormData();
        formData.append('cityid', cityId);

        // Kirim request POST ke API
        fetch('<?php echo site_url('api/location/districts'); ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Reset dropdown
                districtSelect.innerHTML = '<option value="" selected>Pilih Kecamatan</option>';

                // Periksa apakah response berhasil
                if (data.status === 'success' && Array.isArray(data.data)) {
                    // Tambahkan opsi kecamatan dari response
                    data.data.forEach(district => {
                        const option = document.createElement('option');
                        option.value = district.id;
                        option.textContent = district.name;
                        districtSelect.appendChild(option);
                    });
                } else {
                    console.error('Invalid response format:', data);
                }
            })
            .catch(error => {
                console.error('Error loading districts:', error);
                districtSelect.innerHTML = '<option value="" selected>Error loading data</option>';
            });
    }

    // Fungsi untuk memuat data kelurahan/desa
    function loadVillages(districtId) {
        // Tampilkan loading state
        const villageSelect = document.getElementById('village');
        villageSelect.innerHTML = '<option value="">Loading...</option>';

        // Buat form data untuk request
        const formData = new FormData();
        formData.append('districtid', districtId);

        // Kirim request POST ke API
        fetch('<?php echo site_url('api/location/villages'); ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Reset dropdown
                villageSelect.innerHTML = '<option value="" selected>Pilih Kelurahan/Desa</option>';

                // Periksa apakah response berhasil
                if (data.status === 'success' && Array.isArray(data.data)) {
                    // Tambahkan opsi desa dari response
                    data.data.forEach(village => {
                        const option = document.createElement('option');
                        option.value = village.id;
                        option.textContent = village.name;
                        villageSelect.appendChild(option);
                    });
                } else {
                    console.error('Invalid response format:', data);
                }
            })
            .catch(error => {
                console.error('Error loading villages:', error);
                villageSelect.innerHTML = '<option value="" selected>Error loading data</option>';
            });
    }

    // Inisialisasi nilai yang sudah dipilih sebelumnya (jika ada)
    const selectedProvinceId = '<?php echo set_value('province'); ?>';
    const selectedCityId = '<?php echo set_value('city'); ?>';
    const selectedDistrictId = '<?php echo set_value('district'); ?>';
    const selectedVillageId = '<?php echo set_value('village'); ?>';

    // Jika ada nilai provinsi yang dipilih sebelumnya, muat data kota
    if (selectedProvinceId) {
        loadCities(selectedProvinceId);

        // Jika ada nilai kota yang dipilih sebelumnya, muat data kecamatan
        if (selectedCityId) {
            setTimeout(() => {
                document.getElementById('city').value = selectedCityId;
                loadDistricts(selectedCityId);

                // Jika ada nilai kecamatan yang dipilih sebelumnya, muat data desa
                if (selectedDistrictId) {
                    setTimeout(() => {
                        document.getElementById('district').value = selectedDistrictId;
                        loadVillages(selectedDistrictId);

                        // Jika ada nilai desa yang dipilih sebelumnya, pilih desa
                        if (selectedVillageId) {
                            setTimeout(() => {
                                document.getElementById('village').value =
                                    selectedVillageId;
                            }, 1000);
                        }
                    }, 1000);
                }
            }, 1000);
        }
    }
    </script>
</body>

</html>